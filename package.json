{"name": "new-builder", "version": "1.0.0", "description": "Bilingual Website Builder Platform - Microservices Architecture", "private": true, "workspaces": ["Micro-Services/*"], "scripts": {"dev": "concurrently \"npm run dev:auth\" \"npm run dev:builder-editor\" \"npm run dev:builder-api\" \"npm run dev:templates\"", "dev:auth": "cd Micro-Services/auth-service && npm run dev", "dev:builder-editor": "cd Micro-Services/builder-editor && npm run dev", "dev:builder-api": "cd Micro-Services/builder-api && npm run dev", "dev:templates": "cd Micro-Services/templates-service && npm run dev", "dev:media": "cd Micro-Services/media-service && npm run dev", "dev:publish": "cd Micro-Services/publish-service && npm run dev", "dev:domain": "cd Micro-Services/domain-service && npm run dev", "dev:billing": "cd Micro-Services/billing-service && npm run dev", "dev:analytics": "cd Micro-Services/analytics-service && npm run dev", "dev:questionnaire": "cd Micro-Services/questionnaire-service && npm run dev", "dev:invitation": "cd Micro-Services/invitation-service && npm run dev", "dev:geo": "cd Micro-Services/geo-service && npm run dev", "dev:crm": "cd Micro-Services/crm-integration && npm run dev", "dev:admin": "cd Micro-Services/admin-dashboard && npm run dev", "dev:backoffice-api": "cd Micro-Services/backoffice-api && npm run dev", "dev:backoffice-dashboard": "cd Micro-Services/backoffice-dashboard && npm run dev", "dev:site-dashboard": "cd Micro-Services/site-dashboard && npm run dev", "dev:test": "cd Micro-Services/test-service && npm run dev", "build": "npm run build:all", "build:all": "concurrently \"npm run build:auth\" \"npm run build:builder-editor\" \"npm run build:builder-api\" \"npm run build:templates\"", "build:auth": "cd Micro-Services/auth-service && npm run build", "build:builder-editor": "cd Micro-Services/builder-editor && npm run build", "build:builder-api": "cd Micro-Services/builder-api && npm run build", "build:templates": "cd Micro-Services/templates-service && npm run build", "build:media": "cd Micro-Services/media-service && npm run build", "build:publish": "cd Micro-Services/publish-service && npm run build", "build:domain": "cd Micro-Services/domain-service && npm run build", "build:billing": "cd Micro-Services/billing-service && npm run build", "build:analytics": "cd Micro-Services/analytics-service && npm run build", "build:questionnaire": "cd Micro-Services/questionnaire-service && npm run build", "build:invitation": "cd Micro-Services/invitation-service && npm run build", "build:geo": "cd Micro-Services/geo-service && npm run build", "build:crm": "cd Micro-Services/crm-integration && npm run build", "build:admin": "cd Micro-Services/admin-dashboard && npm run build", "build:backoffice-api": "cd Micro-Services/backoffice-api && npm run build", "build:backoffice-dashboard": "cd Micro-Services/backoffice-dashboard && npm run build", "build:site-dashboard": "cd Micro-Services/site-dashboard && npm run build", "build:test": "cd Micro-Services/test-service && npm run build", "start": "npm run start:builder-editor", "start:builder-editor": "cd Micro-Services/builder-editor && npm start", "lint": "npm run lint:all", "lint:all": "concurrently \"npm run lint:auth\" \"npm run lint:builder-editor\" \"npm run lint:builder-api\"", "lint:auth": "cd Micro-Services/auth-service && npm run lint", "lint:builder-editor": "cd Micro-Services/builder-editor && npm run lint", "lint:builder-api": "cd Micro-Services/builder-api && npm run lint", "test": "npm run test:all", "test:all": "concurrently \"npm run test:auth\" \"npm run test:builder-editor\" \"npm run test:builder-api\"", "test:auth": "cd Micro-Services/auth-service && npm test", "test:builder-editor": "cd Micro-Services/builder-editor && npm test", "test:builder-api": "cd Micro-Services/builder-api && npm test", "install:all": "npm install && npm run install:services", "install:services": "powershell -ExecutionPolicy Bypass -File ./install-all-dependencies.ps1", "clean": "npm run clean:all", "clean:all": "rimraf node_modules && npm run clean:services", "clean:services": "find Micro-Services -name node_modules -type d -exec rm -rf {} +", "deploy:vercel": "vercel --prod", "deploy:auth": "cd Micro-Services/auth-service && vercel --prod", "deploy:builder-editor": "cd Micro-Services/builder-editor && vercel --prod", "deploy:builder-api": "cd Micro-Services/builder-api && vercel --prod"}, "keywords": ["website-builder", "bilingual", "arabic", "english", "microservices", "nextjs", "supabase", "vercel"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "devDependencies": {"@types/node": "^20.10.0", "concurrently": "^8.2.2", "rimraf": "^5.0.5", "typescript": "^5.3.0"}, "repository": {"type": "git", "url": "https://github.com/meto002/new-builder.git"}, "bugs": {"url": "https://github.com/meto002/new-builder/issues"}, "homepage": "https://github.com/meto002/new-builder#readme", "dependencies": {"@chakra-ui/icons": "^2.2.4"}}