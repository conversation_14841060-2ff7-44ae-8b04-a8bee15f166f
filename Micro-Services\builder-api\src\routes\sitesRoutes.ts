import express from 'express'
import { z } from 'zod'
import { supabase } from '../../../shared/database'
import { asyncHand<PERSON> } from '../middleware/asyncHandler'
import { AuthenticatedRequest } from '../middleware/authMiddleware'
import { v4 as uuidv4 } from 'uuid'
import slugify from 'slugify'

const router = express.Router()

// Validation schemas
const createSiteSchema = z.object({
  name: z.string().min(1, 'Site name is required'),
  description: z.string().optional(),
  template_id: z.string().uuid().optional(),
  domain: z.string().optional(),
  settings: z.record(z.any()).optional()
})

const updateSiteSchema = z.object({
  name: z.string().min(1).optional(),
  description: z.string().optional(),
  domain: z.string().optional(),
  settings: z.record(z.any()).optional(),
  status: z.enum(['draft', 'published', 'archived']).optional()
})

// Get all sites for user
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { page = 1, limit = 20, search, status } = req.query
  const offset = (Number(page) - 1) * Number(limit)

  let query = supabase
    .from('sites')
    .select('*', { count: 'exact' })
    .eq('user_id', req.user!.id)
    .range(offset, offset + Number(limit) - 1)
    .order('created_at', { ascending: false })

  if (search) {
    query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%`)
  }

  if (status) {
    query = query.eq('status', status)
  }

  const { data, error, count } = await query

  if (error) {
    throw new Error(`Failed to fetch sites: ${error.message}`)
  }

  res.json({
    success: true,
    data: {
      sites: data,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: count || 0,
        totalPages: Math.ceil((count || 0) / Number(limit))
      }
    }
  })
}))

// Get single site
router.get('/:siteId', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { siteId } = req.params

  const { data, error } = await supabase
    .from('sites')
    .select('*')
    .eq('id', siteId)
    .eq('user_id', req.user!.id)
    .single()

  if (error) {
    if (error.code === 'PGRST116') {
      return res.status(404).json({
        success: false,
        error: {
          code: 'SITE_NOT_FOUND',
          message: 'Site not found'
        }
      })
    }
    throw new Error(`Failed to fetch site: ${error.message}`)
  }

  res.json({
    success: true,
    data: { site: data }
  })
}))

// Create new site
router.post('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const validatedData = createSiteSchema.parse(req.body)
  
  const siteData = {
    id: uuidv4(),
    user_id: req.user!.id,
    name: validatedData.name,
    description: validatedData.description,
    slug: slugify(validatedData.name, { lower: true, strict: true }),
    template_id: validatedData.template_id,
    domain: validatedData.domain,
    settings: validatedData.settings || {},
    status: 'draft' as const,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }

  const { data, error } = await supabase
    .from('sites')
    .insert(siteData)
    .select()
    .single()

  if (error) {
    if (error.code === '23505') { // Unique constraint violation
      return res.status(409).json({
        success: false,
        error: {
          code: 'SITE_ALREADY_EXISTS',
          message: 'A site with this name already exists'
        }
      })
    }
    throw new Error(`Failed to create site: ${error.message}`)
  }

  res.status(201).json({
    success: true,
    data: { site: data },
    message: 'Site created successfully'
  })
}))

// Update site
router.put('/:siteId', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { siteId } = req.params
  const validatedData = updateSiteSchema.parse(req.body)

  // Check if site exists and belongs to user
  const { data: existingSite, error: fetchError } = await supabase
    .from('sites')
    .select('id')
    .eq('id', siteId)
    .eq('user_id', req.user!.id)
    .single()

  if (fetchError || !existingSite) {
    return res.status(404).json({
      success: false,
      error: {
        code: 'SITE_NOT_FOUND',
        message: 'Site not found'
      }
    })
  }

  const updateData = {
    ...validatedData,
    updated_at: new Date().toISOString()
  }

  if (validatedData.name) {
    updateData.slug = slugify(validatedData.name, { lower: true, strict: true })
  }

  const { data, error } = await supabase
    .from('sites')
    .update(updateData)
    .eq('id', siteId)
    .eq('user_id', req.user!.id)
    .select()
    .single()

  if (error) {
    throw new Error(`Failed to update site: ${error.message}`)
  }

  res.json({
    success: true,
    data: { site: data },
    message: 'Site updated successfully'
  })
}))

// Delete site
router.delete('/:siteId', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { siteId } = req.params

  // Check if site exists and belongs to user
  const { data: existingSite, error: fetchError } = await supabase
    .from('sites')
    .select('id')
    .eq('id', siteId)
    .eq('user_id', req.user!.id)
    .single()

  if (fetchError || !existingSite) {
    return res.status(404).json({
      success: false,
      error: {
        code: 'SITE_NOT_FOUND',
        message: 'Site not found'
      }
    })
  }

  const { error } = await supabase
    .from('sites')
    .delete()
    .eq('id', siteId)
    .eq('user_id', req.user!.id)

  if (error) {
    throw new Error(`Failed to delete site: ${error.message}`)
  }

  res.json({
    success: true,
    message: 'Site deleted successfully'
  })
}))

// Duplicate site
router.post('/:siteId/duplicate', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { siteId } = req.params
  const { name } = req.body

  if (!name) {
    return res.status(400).json({
      success: false,
      error: {
        code: 'MISSING_NAME',
        message: 'New site name is required'
      }
    })
  }

  // Get original site
  const { data: originalSite, error: fetchError } = await supabase
    .from('sites')
    .select('*')
    .eq('id', siteId)
    .eq('user_id', req.user!.id)
    .single()

  if (fetchError || !originalSite) {
    return res.status(404).json({
      success: false,
      error: {
        code: 'SITE_NOT_FOUND',
        message: 'Original site not found'
      }
    })
  }

  // Create duplicate
  const duplicateData = {
    id: uuidv4(),
    user_id: req.user!.id,
    name,
    description: originalSite.description,
    slug: slugify(name, { lower: true, strict: true }),
    template_id: originalSite.template_id,
    settings: originalSite.settings,
    status: 'draft' as const,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }

  const { data, error } = await supabase
    .from('sites')
    .insert(duplicateData)
    .select()
    .single()

  if (error) {
    throw new Error(`Failed to duplicate site: ${error.message}`)
  }

  res.status(201).json({
    success: true,
    data: { site: data },
    message: 'Site duplicated successfully'
  })
}))

export default router

