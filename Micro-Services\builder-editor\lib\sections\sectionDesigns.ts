// Section designs based on Old Builder's sections-designs package
// This provides the same section templates that customers can use

export interface SectionDesign {
  id: string
  category: string
  name: string
  description: string
  thumbnail: string
  component: any
  meta: any
  styles: any
  defaultProps: Record<string, any>
  defaultStyle: Record<string, any>
}

// Hero section designs (based on Old Builder's hero folder)
export const heroDesigns: SectionDesign[] = [
  {
    id: 'hero-0001',
    category: 'hero',
    name: 'Hero with Image Right',
    description: 'Hero section with title, subtitle, buttons and image on the right',
    thumbnail: '/api/placeholder-image?width=300&height=200&text=Hero%20with%20Image&bg=1a202c&color=ffffff',
    component: null, // Will be implemented
    meta: {
      title: 'Hero Section',
      description: 'Large header section with title, subtitle and CTA',
      category: 'hero'
    },
    styles: {},
    defaultProps: {
      title: 'Welcome to Our Amazing Product',
      subtitle: 'Build something incredible with our powerful website builder',
      primaryButton: {
        text: 'Get Started Free',
        url: '#',
        style: 'primary'
      },
      secondaryButton: {
        text: 'Learn More',
        url: '#',
        style: 'secondary'
      },
      image: {
        src: '/placeholder-hero.jpg',
        alt: 'Hero image'
      },
      socialLinks: []
    },
    defaultStyle: {
      padding: '100px 20px',
      backgroundColor: '#1a202c',
      color: 'white',
      textAlign: 'center',
      minHeight: '500px'
    }
  },
  {
    id: 'hero-0002',
    category: 'hero',
    name: 'Hero Centered',
    description: 'Centered hero section with background image',
    thumbnail: '/api/placeholder-image?width=300&height=200&text=Centered%20Hero&bg=4299e1&color=ffffff',
    component: null,
    meta: {
      title: 'Centered Hero',
      description: 'Centered hero with background image',
      category: 'hero'
    },
    styles: {},
    defaultProps: {
      title: 'Your Success Starts Here',
      subtitle: 'Join thousands of satisfied customers',
      primaryButton: {
        text: 'Start Now',
        url: '#',
        style: 'primary'
      },
      backgroundImage: '/placeholder-hero-bg.jpg'
    },
    defaultStyle: {
      padding: '120px 20px',
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      color: 'white',
      textAlign: 'center',
      minHeight: '600px'
    }
  }
]

// Features section designs
export const featuresDesigns: SectionDesign[] = [
  {
    id: 'features-0001',
    category: 'features',
    name: 'Features Grid',
    description: '3-column features grid with icons',
    thumbnail: '/api/placeholder-image?width=300&height=200&text=Features%20Grid&bg=ffffff&color=333333',
    component: null,
    meta: {
      title: 'Features Section',
      description: 'Showcase your product features',
      category: 'features'
    },
    styles: {},
    defaultProps: {
      title: 'Powerful Features',
      subtitle: 'Everything you need to succeed',
      features: [
        {
          id: '1',
          icon: 'rocket',
          title: 'Lightning Fast',
          description: 'Build websites in minutes, not hours'
        },
        {
          id: '2',
          icon: 'mobile',
          title: 'Responsive Design',
          description: 'Looks perfect on all devices'
        },
        {
          id: '3',
          icon: 'palette',
          title: 'Beautiful Templates',
          description: 'Choose from hundreds of designs'
        }
      ]
    },
    defaultStyle: {
      padding: '80px 20px',
      backgroundColor: '#ffffff'
    }
  }
]

// About section designs
export const aboutDesigns: SectionDesign[] = [
  {
    id: 'about-0001',
    category: 'about',
    name: 'About with Image',
    description: 'About section with text and image',
    thumbnail: '/api/placeholder-image?width=300&height=200&text=About%20Section&bg=f8f9fa&color=333333',
    component: null,
    meta: {
      title: 'About Section',
      description: 'Tell your story',
      category: 'about'
    },
    styles: {},
    defaultProps: {
      title: 'About Our Company',
      content: 'We are passionate about creating amazing experiences...',
      image: {
        src: '/placeholder-about.jpg',
        alt: 'About us'
      }
    },
    defaultStyle: {
      padding: '80px 20px',
      backgroundColor: '#f8f9fa'
    }
  }
]

// Contact section designs
export const contactDesigns: SectionDesign[] = [
  {
    id: 'contact-0001',
    category: 'contact',
    name: 'Contact Form',
    description: 'Contact form with company info',
    thumbnail: '/api/placeholder-image?width=300&height=200&text=Contact%20Form&bg=ffffff&color=333333',
    component: null,
    meta: {
      title: 'Contact Section',
      description: 'Get in touch with your customers',
      category: 'contact'
    },
    styles: {},
    defaultProps: {
      title: 'Get In Touch',
      subtitle: 'We\'d love to hear from you',
      form: {
        fields: ['name', 'email', 'message'],
        submitText: 'Send Message'
      },
      contactInfo: {
        phone: '+****************',
        email: '<EMAIL>',
        address: '123 Main St, City, State 12345'
      }
    },
    defaultStyle: {
      padding: '80px 20px',
      backgroundColor: '#ffffff'
    }
  }
]

// Call-to-action section designs
export const ctaDesigns: SectionDesign[] = [
  {
    id: 'cta-0001',
    category: 'cta',
    name: 'Simple CTA',
    description: 'Simple call-to-action with button',
    thumbnail: '/api/placeholder-image?width=300&height=200&text=Call%20to%20Action&bg=4299e1&color=ffffff',
    component: null,
    meta: {
      title: 'Call to Action',
      description: 'Drive conversions with compelling CTAs',
      category: 'cta'
    },
    styles: {},
    defaultProps: {
      title: 'Ready to Get Started?',
      subtitle: 'Join thousands of satisfied customers today',
      button: {
        text: 'Start Free Trial',
        url: '#',
        style: 'primary'
      }
    },
    defaultStyle: {
      padding: '60px 20px',
      backgroundColor: '#4299e1',
      color: 'white',
      textAlign: 'center'
    }
  }
]

// Footer section designs
export const footerDesigns: SectionDesign[] = [
  {
    id: 'footer-0001',
    category: 'footer',
    name: 'Simple Footer',
    description: 'Footer with links and social media',
    thumbnail: '/api/placeholder-image?width=300&height=200&text=Footer%20Section&bg=2d3748&color=ffffff',
    component: null,
    meta: {
      title: 'Footer Section',
      description: 'Website footer with links and info',
      category: 'footer'
    },
    styles: {},
    defaultProps: {
      logo: {
        src: '/logo.png',
        alt: 'Company Logo'
      },
      links: [
        { text: 'About', url: '/about' },
        { text: 'Services', url: '/services' },
        { text: 'Contact', url: '/contact' },
        { text: 'Privacy', url: '/privacy' }
      ],
      socialLinks: [
        { platform: 'facebook', url: '#' },
        { platform: 'twitter', url: '#' },
        { platform: 'instagram', url: '#' }
      ],
      copyright: '© 2024 Your Company. All rights reserved.'
    },
    defaultStyle: {
      padding: '40px 20px',
      backgroundColor: '#2d3748',
      color: 'white'
    }
  }
]

// All section designs organized by category
export const sectionDesigns = {
  hero: heroDesigns,
  features: featuresDesigns,
  about: aboutDesigns,
  contact: contactDesigns,
  cta: ctaDesigns,
  footer: footerDesigns
}

// Section categories for the sidebar
export const sectionCategories = [
  {
    id: 'hero',
    name: 'Hero',
    description: 'Header sections with titles and CTAs',
    icon: 'star'
  },
  {
    id: 'features',
    name: 'Features',
    description: 'Showcase product features and benefits',
    icon: 'grid'
  },
  {
    id: 'about',
    name: 'About',
    description: 'Tell your story and company info',
    icon: 'info'
  },
  {
    id: 'contact',
    name: 'Contact',
    description: 'Contact forms and information',
    icon: 'phone'
  },
  {
    id: 'cta',
    name: 'Call to Action',
    description: 'Drive conversions with compelling CTAs',
    icon: 'arrow-right'
  },
  {
    id: 'footer',
    name: 'Footer',
    description: 'Website footers with links and info',
    icon: 'layout'
  }
]

// Helper function to get section design by category and id
export function getSectionDesign(category: string, designId: string): SectionDesign | null {
  const categoryDesigns = sectionDesigns[category as keyof typeof sectionDesigns]
  if (!categoryDesigns) return null
  
  return categoryDesigns.find(design => design.id === designId) || null
}

// Helper function to get all designs for a category
export function getSectionsByCategory(category: string): SectionDesign[] {
  return sectionDesigns[category as keyof typeof sectionDesigns] || []
}
