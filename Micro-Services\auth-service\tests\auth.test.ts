import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest'
import request from 'supertest'
import app from '../src/index'
import { supabaseAdmin } from '../src/lib/supabaseClient'

describe('Auth Service', () => {
  const testUser = {
    email: '<EMAIL>',
    password: 'TestPassword123',
    full_name: 'Test User'
  }

  let authToken: string
  let refreshToken: string
  let userId: string

  beforeAll(async () => {
    // Clean up any existing test user
    try {
      const { data: users } = await supabaseAdmin
        .from('users')
        .select('id')
        .eq('email', testUser.email)
      
      if (users && users.length > 0) {
        await supabaseAdmin.auth.admin.deleteUser(users[0].id)
      }
    } catch (error) {
      // Ignore cleanup errors
    }
  })

  afterAll(async () => {
    // Clean up test user
    if (userId) {
      try {
        await supabaseAdmin.auth.admin.deleteUser(userId)
      } catch (error) {
        // Ignore cleanup errors
      }
    }
  })

  describe('POST /auth/register', () => {
    it('should register a new user successfully', async () => {
      const response = await request(app)
        .post('/auth/register')
        .send(testUser)
        .expect(201)

      expect(response.body.success).toBe(true)
      expect(response.body.data.user).toBeDefined()
      expect(response.body.data.user.email).toBe(testUser.email)
      expect(response.body.data.user.full_name).toBe(testUser.full_name)
      expect(response.body.data.tokens).toBeDefined()
      expect(response.body.data.tokens.accessToken).toBeDefined()
      expect(response.body.data.tokens.refreshToken).toBeDefined()

      userId = response.body.data.user.id
      authToken = response.body.data.tokens.accessToken
      refreshToken = response.body.data.tokens.refreshToken
    })

    it('should fail with invalid email', async () => {
      const response = await request(app)
        .post('/auth/register')
        .send({
          ...testUser,
          email: 'invalid-email'
        })
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('VALIDATION_ERROR')
    })

    it('should fail with short password', async () => {
      const response = await request(app)
        .post('/auth/register')
        .send({
          ...testUser,
          email: '<EMAIL>',
          password: '123'
        })
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('VALIDATION_ERROR')
    })

    it('should fail with duplicate email', async () => {
      const response = await request(app)
        .post('/auth/register')
        .send(testUser)
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('REGISTRATION_FAILED')
    })
  })

  describe('POST /auth/login', () => {
    it('should login successfully with valid credentials', async () => {
      const response = await request(app)
        .post('/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        })
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.user).toBeDefined()
      expect(response.body.data.user.email).toBe(testUser.email)
      expect(response.body.data.tokens).toBeDefined()

      authToken = response.body.data.tokens.accessToken
      refreshToken = response.body.data.tokens.refreshToken
    })

    it('should fail with invalid credentials', async () => {
      const response = await request(app)
        .post('/auth/login')
        .send({
          email: testUser.email,
          password: 'wrongpassword'
        })
        .expect(401)

      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('INVALID_CREDENTIALS')
    })

    it('should fail with non-existent user', async () => {
      const response = await request(app)
        .post('/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'password123'
        })
        .expect(401)

      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('INVALID_CREDENTIALS')
    })
  })

  describe('GET /auth/me', () => {
    it('should return current user with valid token', async () => {
      const response = await request(app)
        .get('/auth/me')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.user).toBeDefined()
      expect(response.body.data.user.email).toBe(testUser.email)
    })

    it('should fail without authorization header', async () => {
      const response = await request(app)
        .get('/auth/me')
        .expect(401)

      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('UNAUTHORIZED')
    })

    it('should fail with invalid token', async () => {
      const response = await request(app)
        .get('/auth/me')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401)

      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('UNAUTHORIZED')
    })
  })

  describe('PUT /auth/profile', () => {
    it('should update user profile successfully', async () => {
      const updates = {
        full_name: 'Updated Test User',
        language: 'es',
        timezone: 'America/New_York'
      }

      const response = await request(app)
        .put('/auth/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .send(updates)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.user.full_name).toBe(updates.full_name)
      expect(response.body.data.user.language).toBe(updates.language)
      expect(response.body.data.user.timezone).toBe(updates.timezone)
    })

    it('should fail without authorization', async () => {
      const response = await request(app)
        .put('/auth/profile')
        .send({ full_name: 'New Name' })
        .expect(401)

      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('UNAUTHORIZED')
    })
  })

  describe('POST /auth/refresh', () => {
    it('should refresh token successfully', async () => {
      const response = await request(app)
        .post('/auth/refresh')
        .send({ refreshToken })
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.tokens).toBeDefined()
      expect(response.body.data.tokens.accessToken).toBeDefined()
      expect(response.body.data.tokens.refreshToken).toBeDefined()

      // Update tokens for subsequent tests
      authToken = response.body.data.tokens.accessToken
      refreshToken = response.body.data.tokens.refreshToken
    })

    it('should fail with invalid refresh token', async () => {
      const response = await request(app)
        .post('/auth/refresh')
        .send({ refreshToken: 'invalid-refresh-token' })
        .expect(401)

      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('INVALID_REFRESH_TOKEN')
    })

    it('should fail without refresh token', async () => {
      const response = await request(app)
        .post('/auth/refresh')
        .send({})
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('MISSING_REFRESH_TOKEN')
    })
  })

  describe('POST /auth/logout', () => {
    it('should logout successfully', async () => {
      const response = await request(app)
        .post('/auth/logout')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.message).toBe('Logout successful')
    })

    it('should fail without authorization', async () => {
      const response = await request(app)
        .post('/auth/logout')
        .expect(401)

      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('UNAUTHORIZED')
    })
  })

  describe('POST /auth/forgot-password', () => {
    it('should send password reset email', async () => {
      const response = await request(app)
        .post('/auth/forgot-password')
        .send({ email: testUser.email })
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.message).toBe('Password reset email sent')
    })

    it('should fail without email', async () => {
      const response = await request(app)
        .post('/auth/forgot-password')
        .send({})
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('MISSING_EMAIL')
    })
  })
})

describe('Health Check', () => {
  it('should return health status', async () => {
    const response = await request(app)
      .get('/health')
      .expect(200)

    expect(response.body.status).toBe('healthy')
    expect(response.body.service).toBe('auth-service')
    expect(response.body.version).toBe('1.0.0')
  })
})

describe('Service Info', () => {
  it('should return service information', async () => {
    const response = await request(app)
      .get('/info')
      .expect(200)

    expect(response.body.success).toBe(true)
    expect(response.body.data.service).toBe('auth-service')
    expect(response.body.data.version).toBe('1.0.0')
  })
})

