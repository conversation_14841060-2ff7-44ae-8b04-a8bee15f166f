'use client'

import { useEffect } from 'react'
import { useEditorStore } from '@/lib/stores/editorStore'
import EditorPage from '../page'

// Demo page with sample data to test the editor
export default function DemoEditorPage() {
  const { setCurrentPage } = useEditorStore()

  useEffect(() => {
    // Create a demo page with sample content
    const demoPage = {
      id: 'demo-page-1',
      name: 'Demo Landing Page',
      slug: 'demo-landing',
      sections: [],
      seoSettings: {
        title: 'Demo Landing Page - Website Builder',
        description: 'A demo landing page showcasing the website builder capabilities',
        keywords: ['website builder', 'demo', 'landing page', 'responsive']
      },
      settings: {
        customCSS: '/* Custom styles for demo page */',
        customJS: '// Custom JavaScript for demo page'
      }
    }

    setCurrentPage(demoPage)
  }, [setCurrentPage])

  return <EditorPage />
}
