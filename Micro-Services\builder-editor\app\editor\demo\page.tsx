'use client'

import { useEffect } from 'react'
import { useEditorStore } from '@/lib/stores/editorStore'
import EditorPage from '../page'

// Demo page with sample data to test the editor
export default function DemoEditorPage() {
  const { setCurrentPage } = useEditorStore()

  useEffect(() => {
    // Create a demo page with sample content
    const demoPage = {
      id: 'demo-page-1',
      name: 'Demo Landing Page',
      slug: 'demo-landing',
      sections: [
        {
          id: 'hero-section-1',
          type: 'hero' as const,
          name: 'Hero Section',
          elements: [
            {
              id: 'hero-title',
              type: 'text' as const,
              props: {
                content: 'Welcome to Our Amazing Product',
                fontSize: '48px',
                fontWeight: 'bold',
                textAlign: 'center'
              },
              style: {
                marginBottom: '20px',
                color: '#ffffff'
              }
            },
            {
              id: 'hero-subtitle',
              type: 'text' as const,
              props: {
                content: 'Build something incredible with our powerful website builder',
                fontSize: '20px',
                textAlign: 'center'
              },
              style: {
                marginBottom: '40px',
                color: '#ffffff',
                opacity: 0.9
              }
            },
            {
              id: 'hero-cta',
              type: 'button' as const,
              props: {
                text: 'Get Started Free',
                colorScheme: 'blue',
                size: 'lg'
              },
              style: {}
            }
          ],
          style: {
            padding: '100px 20px',
            backgroundColor: '#1a202c',
            color: 'white',
            textAlign: 'center',
            minHeight: '500px',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center'
          },
          responsive: {
            desktop: {},
            tablet: {
              padding: '80px 20px'
            },
            mobile: {
              padding: '60px 20px'
            }
          }
        },
        {
          id: 'features-section-1',
          type: 'feature' as const,
          name: 'Features Section',
          elements: [
            {
              id: 'features-title',
              type: 'text' as const,
              props: {
                content: 'Powerful Features',
                fontSize: '36px',
                fontWeight: 'bold',
                textAlign: 'center'
              },
              style: {
                marginBottom: '60px'
              }
            },
            {
              id: 'feature-1',
              type: 'text' as const,
              props: {
                content: '🚀 Lightning Fast - Build websites in minutes, not hours',
                fontSize: '18px'
              },
              style: {
                marginBottom: '20px'
              }
            },
            {
              id: 'feature-2',
              type: 'text' as const,
              props: {
                content: '📱 Responsive Design - Looks perfect on all devices',
                fontSize: '18px'
              },
              style: {
                marginBottom: '20px'
              }
            },
            {
              id: 'feature-3',
              type: 'text' as const,
              props: {
                content: '🎨 Beautiful Templates - Choose from hundreds of designs',
                fontSize: '18px'
              },
              style: {
                marginBottom: '20px'
              }
            }
          ],
          style: {
            padding: '80px 20px',
            backgroundColor: '#ffffff',
            maxWidth: '1200px',
            margin: '0 auto'
          },
          responsive: {
            desktop: {},
            tablet: {
              padding: '60px 20px'
            },
            mobile: {
              padding: '40px 20px'
            }
          }
        }
      ],
      seoSettings: {
        title: 'Demo Landing Page - Website Builder',
        description: 'A demo landing page showcasing the website builder capabilities',
        keywords: ['website builder', 'demo', 'landing page', 'responsive']
      },
      settings: {
        customCSS: '/* Custom styles for demo page */',
        customJS: '// Custom JavaScript for demo page'
      }
    }

    setCurrentPage(demoPage)
  }, [setCurrentPage])

  return <EditorPage />
}
