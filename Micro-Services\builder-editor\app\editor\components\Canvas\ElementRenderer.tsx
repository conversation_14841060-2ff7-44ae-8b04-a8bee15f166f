'use client'

import { useState } from 'react'
import {
  Box,
  Text,
  Image,
  Button,
  useColorModeValue
} from '@chakra-ui/react'
import { motion } from 'framer-motion'
import { Element, useEditorStore } from '@/lib/stores/editorStore'

// Import enhanced components
import BackgroundContainer from '@/components/elements/BackgroundContainer/BackgroundContainer'
import HTMLText from '@/components/elements/HTMLText/HTMLText'
import Form from '@/components/elements/Form/Form'
import EnhancedImage from '@/components/elements/Image/Image'
import Video from '@/components/elements/Video/Video'
import SlideShow from '@/components/elements/SlideShow/SlideShow'
import SocialLinks from '@/components/elements/SocialLinks/SocialLinks'
import NavMenu from '@/components/elements/NavMenu/NavMenu'
import Map from '@/components/elements/Map/Map'
import Icon from '@/components/elements/Icon/Icon'

interface ElementRendererProps {
  element: Element
  sectionId: string
}

export function ElementRenderer({ element, sectionId }: ElementRendererProps) {
  const [isHovered, setIsHovered] = useState(false)
  
  const {
    selectedElement,
    selectElement,
    updateElement,
    currentBreakpoint
  } = useEditorStore()

  const isSelected = selectedElement?.id === element.id
  const borderColor = useColorModeValue('blue.400', 'blue.300')
  const hoverBorderColor = useColorModeValue('gray.300', 'gray.600')

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    selectElement(element)
  }

  const renderElementContent = () => {
    switch (element.type) {
      case 'text':
        // Use basic text for simple cases, HTMLText for rich content
        if (element.props.richText) {
          return (
            <HTMLText
              settings={{
                content: element.props.content || '',
                fontSize: element.props.fontSize || '16px',
                fontFamily: element.props.fontFamily || 'inherit',
                fontWeight: element.props.fontWeight || 'normal',
                color: element.props.color || '#000000',
                textAlign: element.props.textAlign || 'left',
                lineHeight: element.props.lineHeight || '1.5',
                letterSpacing: element.props.letterSpacing || 'normal',
                textDecoration: element.props.textDecoration || 'none',
                textTransform: element.props.textTransform || 'none'
              }}
              onChange={(settings) => {
                updateElement(element.id, {
                  props: {
                    ...element.props,
                    ...settings
                  }
                })
              }}
              isEditing={isSelected}
              style={element.style}
            />
          )
        }
        return (
          <Text
            {...element.props}
            style={element.style}
          >
            {element.props.content || 'Click to edit text'}
          </Text>
        )

      case 'image':
        return (
          <EnhancedImage
            settings={{
              src: element.props.src || '',
              alt: element.props.alt || '',
              width: element.props.width || 'auto',
              height: element.props.height || 'auto',
              objectFit: element.props.objectFit || 'cover',
              objectPosition: element.props.objectPosition || 'center',
              borderRadius: element.props.borderRadius || '0',
              border: element.props.border || { width: '0', style: 'solid', color: '#000000' },
              shadow: element.props.shadow || { enabled: false, x: 0, y: 0, blur: 0, spread: 0, color: '#000000' },
              filters: element.props.filters || {
                brightness: 100,
                contrast: 100,
                saturation: 100,
                blur: 0,
                grayscale: 0,
                sepia: 0,
                hueRotate: 0
              },
              overlay: element.props.overlay || { enabled: false, color: '#000000', opacity: 50, blendMode: 'normal' },
              lazy: element.props.lazy || true,
              clickAction: element.props.clickAction || 'none',
              linkUrl: element.props.linkUrl,
              lightboxCaption: element.props.lightboxCaption
            }}
            onChange={(settings) => {
              updateElement(element.id, {
                props: {
                  ...element.props,
                  ...settings
                }
              })
            }}
            isEditing={isSelected}
            style={element.style}
          />
        )

      case 'button':
        return (
          <Button
            {...element.props}
            style={element.style}
          >
            {element.props.text || 'Button'}
          </Button>
        )

      case 'video':
        return (
          <Video
            settings={{
              src: element.props.src || '',
              type: element.props.type || 'upload',
              poster: element.props.poster,
              width: element.props.width || '100%',
              height: element.props.height || 'auto',
              aspectRatio: element.props.aspectRatio || '16:9',
              autoplay: element.props.autoplay || false,
              loop: element.props.loop || false,
              muted: element.props.muted || false,
              controls: element.props.controls !== false,
              preload: element.props.preload || 'metadata',
              playsinline: element.props.playsinline || true,
              borderRadius: element.props.borderRadius || '0',
              shadow: element.props.shadow || { enabled: false, x: 0, y: 0, blur: 0, spread: 0, color: '#000000' },
              overlay: element.props.overlay || {
                enabled: false,
                color: '#000000',
                opacity: 50,
                showPlayButton: true,
                playButtonSize: 'md',
                playButtonColor: 'blue'
              },
              responsive: element.props.responsive !== false,
              maxWidth: element.props.maxWidth || '100%'
            }}
            onChange={(settings) => {
              updateElement(element.id, {
                props: {
                  ...element.props,
                  ...settings
                }
              })
            }}
            isEditing={isSelected}
            style={element.style}
          />
        )

      case 'form':
        return (
          <Form
            settings={{
              title: element.props.title || 'Contact Form',
              description: element.props.description,
              fields: element.props.fields || [
                {
                  id: 'name',
                  type: 'text',
                  label: 'Name',
                  placeholder: 'Your name',
                  required: true,
                  width: 'full'
                },
                {
                  id: 'email',
                  type: 'email',
                  label: 'Email',
                  placeholder: '<EMAIL>',
                  required: true,
                  width: 'full'
                },
                {
                  id: 'message',
                  type: 'textarea',
                  label: 'Message',
                  placeholder: 'Your message',
                  required: true,
                  width: 'full'
                }
              ],
              submitButton: element.props.submitButton || {
                text: 'Submit',
                color: 'blue',
                size: 'md'
              },
              styling: element.props.styling || {
                backgroundColor: '#ffffff',
                borderColor: '#e2e8f0',
                borderRadius: '8px',
                padding: '24px',
                spacing: '16px'
              },
              behavior: element.props.behavior || {
                action: '',
                method: 'POST',
                successMessage: 'Thank you for your message!',
                errorMessage: 'An error occurred. Please try again.'
              }
            }}
            onChange={(settings) => {
              updateElement(element.id, {
                props: {
                  ...element.props,
                  ...settings
                }
              })
            }}
            isEditing={isSelected}
            style={element.style}
          />
        )
      
      case 'map':
        return (
          <Map
            settings={{
              location: element.props.location || {
                address: 'New York, NY, USA',
                lat: 40.7128,
                lng: -74.0060
              },
              zoom: element.props.zoom || 12,
              mapType: element.props.mapType || 'roadmap',
              style: element.props.style || {
                width: '100%',
                height: '400px',
                borderRadius: 8,
                border: '1px solid #e2e8f0'
              },
              marker: element.props.marker || {
                enabled: true,
                color: '#ff0000',
                title: 'Our Location',
                description: 'Visit us here!'
              },
              controls: element.props.controls || {
                zoomControl: true,
                streetViewControl: true,
                fullscreenControl: true,
                mapTypeControl: true
              }
            }}
            onChange={(settings) => {
              updateElement(element.id, {
                props: {
                  ...element.props,
                  ...settings
                }
              })
            }}
            isEditing={isSelected}
            style={element.style}
          />
        )
      
      case 'social':
        return (
          <SocialLinks
            settings={{
              platforms: element.props.platforms || [
                {
                  id: 'facebook',
                  name: 'Facebook',
                  url: element.props.facebookUrl || '',
                  icon: '📘',
                  color: '#1877F2',
                  enabled: true
                },
                {
                  id: 'twitter',
                  name: 'Twitter',
                  url: element.props.twitterUrl || '',
                  icon: '🐦',
                  color: '#1DA1F2',
                  enabled: true
                },
                {
                  id: 'instagram',
                  name: 'Instagram',
                  url: element.props.instagramUrl || '',
                  icon: '📷',
                  color: '#E4405F',
                  enabled: true
                }
              ],
              layout: element.props.layout || 'horizontal',
              alignment: element.props.alignment || 'center',
              size: element.props.size || 'md',
              style: element.props.linkStyle || 'icon',
              spacing: element.props.spacing || 8,
              borderRadius: element.props.borderRadius || '8px',
              colors: element.props.colors || {
                useCustom: false,
                background: '#000000',
                text: '#ffffff',
                hover: {
                  background: '#333333',
                  text: '#ffffff'
                }
              },
              animation: element.props.animation || {
                enabled: true,
                type: 'scale',
                duration: 300
              },
              target: element.props.target || '_blank'
            }}
            onChange={(settings) => {
              updateElement(element.id, {
                props: {
                  ...element.props,
                  ...settings
                }
              })
            }}
            isEditing={isSelected}
            style={element.style}
          />
        )

      case 'slideshow':
        return (
          <SlideShow
            settings={{
              slides: element.props.slides || [],
              autoplay: element.props.autoplay !== false,
              autoplaySpeed: element.props.autoplaySpeed || 3,
              showArrows: element.props.showArrows !== false,
              showDots: element.props.showDots !== false,
              infinite: element.props.infinite !== false,
              slidesToShow: element.props.slidesToShow || 1,
              slidesToScroll: element.props.slidesToScroll || 1,
              aspectRatio: element.props.aspectRatio || '16:9',
              height: element.props.height || 'auto',
              borderRadius: element.props.borderRadius || '0',
              shadow: element.props.shadow || { enabled: false, x: 0, y: 0, blur: 0, spread: 0, color: '#000000' },
              transition: element.props.transition || {
                type: 'slide',
                duration: 500,
                easing: 'ease-in-out'
              },
              controls: element.props.controls || {
                arrowColor: 'white',
                arrowSize: 'md',
                arrowPosition: 'inside',
                dotColor: 'white',
                dotSize: 'md',
                dotPosition: 'bottom'
              }
            }}
            onChange={(settings) => {
              updateElement(element.id, {
                props: {
                  ...element.props,
                  ...settings
                }
              })
            }}
            isEditing={isSelected}
            style={element.style}
          />
        )

      case 'navmenu':
        return (
          <NavMenu
            settings={{
              items: element.props.items || [
                { id: '1', label: 'Home', url: '/', target: '_self' },
                { id: '2', label: 'About', url: '/about', target: '_self' },
                { id: '3', label: 'Services', url: '/services', target: '_self' },
                { id: '4', label: 'Contact', url: '/contact', target: '_self' }
              ],
              layout: element.props.layout || 'horizontal',
              alignment: element.props.alignment || 'left',
              style: element.props.style || {
                backgroundColor: 'transparent',
                textColor: '#333333',
                hoverColor: '#0066cc',
                fontSize: 16,
                fontWeight: 'normal',
                padding: 12,
                borderRadius: 0
              },
              mobile: element.props.mobile || {
                enabled: true,
                breakpoint: 768,
                hamburgerColor: '#333333'
              }
            }}
            onChange={(settings) => {
              updateElement(element.id, {
                props: {
                  ...element.props,
                  ...settings
                }
              })
            }}
            isEditing={isSelected}
            style={element.style}
          />
        )

      case 'icon':
        return (
          <Icon
            settings={{
              name: element.props.name || 'home',
              collection: element.props.collection || 'general',
              size: element.props.size || 24,
              color: element.props.color || '#333333',
              style: element.props.style || {
                backgroundColor: 'transparent',
                borderRadius: 0,
                padding: 8,
                border: 'none',
                shadow: 'none'
              },
              animation: element.props.animation || {
                enabled: false,
                type: 'none',
                duration: 1
              },
              link: element.props.link || {
                enabled: false,
                url: '',
                target: '_self'
              }
            }}
            onChange={(settings) => {
              updateElement(element.id, {
                props: {
                  ...element.props,
                  ...settings
                }
              })
            }}
            isEditing={isSelected}
            style={element.style}
          />
        )

      case 'custom':
        return (
          <Box
            p={4}
            border="2px dashed"
            borderColor="gray.300"
            borderRadius="md"
            textAlign="center"
            {...element.props}
            style={element.style}
          >
            <Text color="gray.500">
              Custom Element - {element.props.name || 'Unnamed'}
            </Text>
          </Box>
        )
      
      default:
        return (
          <Box
            p={4}
            bg="gray.50"
            border="1px solid"
            borderColor="gray.200"
            borderRadius="md"
            {...element.props}
            style={element.style}
          >
            <Text fontSize="sm" color="gray.500">
              Unknown element type: {element.type}
            </Text>
          </Box>
        )
    }
  }

  return (
    <motion.div
      layout
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      whileHover={{ scale: 1.02 }}
      transition={{ duration: 0.2 }}
    >
      <Box
        position="relative"
        border={
          isSelected 
            ? `2px solid ${borderColor}` 
            : isHovered 
            ? `1px solid ${hoverBorderColor}` 
            : 'none'
        }
        borderRadius={isSelected || isHovered ? 'md' : 'none'}
        cursor="pointer"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onClick={handleClick}
        _hover={{
          boxShadow: isSelected ? 'none' : 'sm'
        }}
      >
        {/* Element selection indicator */}
        {isSelected && (
          <Box
            position="absolute"
            top="-8px"
            left="-2px"
            bg={borderColor}
            color="white"
            fontSize="xs"
            px={2}
            py={1}
            borderRadius="sm"
            zIndex={5}
          >
            {element.type}
          </Box>
        )}
        
        {/* Element content */}
        {renderElementContent()}
        
        {/* Hover overlay */}
        {isHovered && !isSelected && (
          <Box
            position="absolute"
            top="0"
            left="0"
            right="0"
            bottom="0"
            bg="blue.50"
            opacity="0.1"
            pointerEvents="none"
            borderRadius="md"
          />
        )}
      </Box>
    </motion.div>
  )
}
