{"name": "auth-service", "version": "1.0.0", "description": "Authentication and authorization service for New Builder", "main": "dist/index.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "vitest", "test:watch": "vitest --watch", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix"}, "dependencies": {"@supabase/supabase-js": "^2.45.4", "@supabase/ssr": "^0.5.1", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "zod": "^3.22.4", "bcryptjs": "^2.4.3", "dotenv": "^16.3.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/cors": "^2.8.17", "@types/bcryptjs": "^2.4.6", "@types/node": "^20.10.0", "typescript": "^5.3.2", "ts-node-dev": "^2.0.0", "vitest": "^1.0.0", "supertest": "^6.3.3", "@types/supertest": "^2.0.16", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1"}}