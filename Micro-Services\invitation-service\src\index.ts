import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import rateLimit from 'express-rate-limit'
import dotenv from 'dotenv'
import mainRoutes from './routes/mainRoutes'
import { errorHandler } from './middleware/errorHandler'
import { requestLogger } from './middleware/requestLogger'
import { authMiddleware } from './middleware/authMiddleware'

dotenv.config()

const app = express()
const PORT = process.env.PORT || 3010

app.use(helmet())
app.use(cors({
  origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
  credentials: true
}))

app.use(rateLimit({
  windowMs: 15 * 60 * 1000,
  max: 100
}))

app.use(express.json({ limit: '10mb' }))
app.use(requestLogger)

app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    service: 'invitation-service',
    timestamp: new Date().toISOString()
  })
})

app.use('/', mainRoutes)

app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: { code: 'NOT_FOUND', message: 'Route not found' }
  })
})

app.use(errorHandler)

app.listen(PORT, () => {
  console.log(`🚀 invitation-service running on port ${PORT}`)
})

export default app
