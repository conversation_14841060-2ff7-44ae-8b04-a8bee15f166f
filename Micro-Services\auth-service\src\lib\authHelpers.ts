import { Request, Response, NextFunction } from 'express'
import jwt from 'jsonwebtoken'
import { supabaseAdmin, User, UserProfile } from './supabaseClient'
import { z } from 'zod'

const JWT_SECRET = process.env.JWT_SECRET!

if (!JWT_SECRET) {
  throw new Error('JWT_SECRET environment variable is required')
}

// Validation schemas
export const loginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(6, 'Password must be at least 6 characters')
})

export const registerSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  full_name: z.string().min(1, 'Full name is required').optional(),
  language: z.string().optional(),
  timezone: z.string().optional()
})

export const updateProfileSchema = z.object({
  full_name: z.string().optional(),
  avatar_url: z.string().url().optional(),
  language: z.string().optional(),
  timezone: z.string().optional(),
  preferences: z.record(z.any()).optional()
})

// JWT token utilities
export function generateTokens(user: UserProfile) {
  const payload = {
    id: user.id,
    email: user.email,
    role: user.role
  }

  const accessToken = jwt.sign(payload, JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRES_IN || '7d',
    issuer: 'new-builder-auth',
    audience: 'new-builder-services'
  })

  const refreshToken = jwt.sign(
    { id: user.id, type: 'refresh' },
    JWT_SECRET,
    {
      expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '30d',
      issuer: 'new-builder-auth',
      audience: 'new-builder-services'
    }
  )

  return { accessToken, refreshToken }
}

export function verifyToken(token: string): any {
  try {
    return jwt.verify(token, JWT_SECRET, {
      issuer: 'new-builder-auth',
      audience: 'new-builder-services'
    })
  } catch (error) {
    throw new Error('Invalid or expired token')
  }
}

// Extract user from request header
export function getUserFromHeader(req: Request): any {
  const authHeader = req.headers.authorization
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('Missing or invalid authorization header')
  }

  const token = authHeader.split(' ')[1]
  return verifyToken(token)
}

// Middleware functions
export function requireAuth(req: Request, res: Response, next: NextFunction) {
  try {
    const user = getUserFromHeader(req)
    req.user = user
    next()
  } catch (error) {
    res.status(401).json({
      success: false,
      error: {
        code: 'UNAUTHORIZED',
        message: 'Authentication required'
      }
    })
  }
}

export function requireRole(allowedRoles: string | string[]) {
  const roles = Array.isArray(allowedRoles) ? allowedRoles : [allowedRoles]
  
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const user = getUserFromHeader(req)
      
      if (!roles.includes(user.role)) {
        return res.status(403).json({
          success: false,
          error: {
            code: 'FORBIDDEN',
            message: 'Insufficient permissions'
          }
        })
      }
      
      req.user = user
      next()
    } catch (error) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required'
        }
      })
    }
  }
}

// User management functions
export async function createUserProfile(userData: {
  id: string
  email: string
  full_name?: string
  language?: string
  timezone?: string
}): Promise<UserProfile> {
  const { data, error } = await supabaseAdmin
    .from('users')
    .insert({
      id: userData.id,
      email: userData.email,
      full_name: userData.full_name,
      language: userData.language || 'en',
      timezone: userData.timezone || 'UTC',
      role: 'user'
    })
    .select()
    .single()

  if (error) {
    throw new Error(`Failed to create user profile: ${error.message}`)
  }

  return data
}

export async function getUserProfile(userId: string): Promise<UserProfile | null> {
  const { data, error } = await supabaseAdmin
    .from('users')
    .select('*')
    .eq('id', userId)
    .single()

  if (error) {
    if (error.code === 'PGRST116') {
      return null // User not found
    }
    throw new Error(`Failed to get user profile: ${error.message}`)
  }

  return data
}

export async function updateUserProfile(
  userId: string,
  updates: Partial<UserProfile>
): Promise<UserProfile> {
  const { data, error } = await supabaseAdmin
    .from('users')
    .update({
      ...updates,
      updated_at: new Date().toISOString()
    })
    .eq('id', userId)
    .select()
    .single()

  if (error) {
    throw new Error(`Failed to update user profile: ${error.message}`)
  }

  return data
}

export async function deleteUserProfile(userId: string): Promise<void> {
  const { error } = await supabaseAdmin
    .from('users')
    .delete()
    .eq('id', userId)

  if (error) {
    throw new Error(`Failed to delete user profile: ${error.message}`)
  }
}

// Session management
export async function createSession(userId: string, metadata?: Record<string, any>) {
  // Store session information if needed
  // This could be expanded to include session tracking in the database
  return {
    userId,
    createdAt: new Date().toISOString(),
    metadata: metadata || {}
  }
}

export async function invalidateSession(userId: string) {
  // Invalidate user sessions
  // This could be expanded to blacklist tokens or update session status
  return true
}

// Password utilities (for future use if needed)
export function validatePassword(password: string): boolean {
  // Password validation rules
  const minLength = 6
  const hasUpperCase = /[A-Z]/.test(password)
  const hasLowerCase = /[a-z]/.test(password)
  const hasNumbers = /\d/.test(password)
  
  return password.length >= minLength && hasUpperCase && hasLowerCase && hasNumbers
}

// Extend Express Request type
declare global {
  namespace Express {
    interface Request {
      user?: any
    }
  }
}

