'use client'

import React, { useState } from 'react'
import {
  Box,
  VStack,
  HStack,
  Text,
  Input,
  Select,
  Button,
  FormControl,
  FormLabel,
  Switch,
  IconButton,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  useColorModeValue,
  Flex,
  Link
} from '@chakra-ui/react'
import { 
  AddIcon, 
  DeleteIcon, 
  SettingsIcon,
  ExternalLinkIcon
} from '@chakra-ui/icons'

export interface SocialPlatform {
  id: string
  name: string
  url: string
  icon: string
  color: string
  enabled: boolean
}

export interface SocialLinksSettings {
  platforms: SocialPlatform[]
  layout: 'horizontal' | 'vertical' | 'grid'
  alignment: 'left' | 'center' | 'right'
  size: 'sm' | 'md' | 'lg' | 'xl'
  style: 'icon' | 'button' | 'text' | 'icon-text'
  spacing: number
  borderRadius: string
  colors: {
    useCustom: boolean
    background: string
    text: string
    hover: {
      background: string
      text: string
    }
  }
  animation: {
    enabled: boolean
    type: 'bounce' | 'scale' | 'rotate' | 'pulse'
    duration: number
  }
  target: '_blank' | '_self'
}

interface SocialLinksProps {
  settings: SocialLinksSettings
  onChange?: (settings: SocialLinksSettings) => void
  isEditing?: boolean
  className?: string
  style?: React.CSSProperties
}

// Default social platforms with their brand colors
const DEFAULT_PLATFORMS = [
  { name: 'Facebook', icon: '📘', color: '#1877F2' },
  { name: 'Twitter', icon: '🐦', color: '#1DA1F2' },
  { name: 'Instagram', icon: '📷', color: '#E4405F' },
  { name: 'LinkedIn', icon: '💼', color: '#0A66C2' },
  { name: 'YouTube', icon: '📺', color: '#FF0000' },
  { name: 'TikTok', icon: '🎵', color: '#000000' },
  { name: 'WhatsApp', icon: '💬', color: '#25D366' },
  { name: 'Telegram', icon: '✈️', color: '#0088CC' },
  { name: 'Discord', icon: '🎮', color: '#5865F2' },
  { name: 'Snapchat', icon: '👻', color: '#FFFC00' },
  { name: 'Pinterest', icon: '📌', color: '#BD081C' },
  { name: 'Reddit', icon: '🤖', color: '#FF4500' },
  { name: 'GitHub', icon: '🐙', color: '#181717' },
  { name: 'Dribbble', icon: '🏀', color: '#EA4C89' },
  { name: 'Behance', icon: '🎨', color: '#1769FF' }
]

export function SocialLinks({
  settings,
  onChange,
  isEditing = false,
  className,
  style
}: SocialLinksProps) {
  const { isOpen, onOpen, onClose } = useDisclosure()
  const borderColor = useColorModeValue('gray.200', 'gray.700')
  const bgColor = useColorModeValue('gray.50', 'gray.800')

  const handleSettingsChange = (key: keyof SocialLinksSettings, value: any) => {
    onChange?.({
      ...settings,
      [key]: value
    })
  }

  const handleNestedSettingsChange = (
    parent: keyof SocialLinksSettings,
    key: string,
    value: any
  ) => {
    onChange?.({
      ...settings,
      [parent]: {
        ...(settings[parent] as any),
        [key]: value
      }
    })
  }

  const handlePlatformChange = (index: number, updates: Partial<SocialPlatform>) => {
    const newPlatforms = [...settings.platforms]
    newPlatforms[index] = { ...newPlatforms[index], ...updates }
    handleSettingsChange('platforms', newPlatforms)
  }

  const addPlatform = (platformName: string) => {
    const defaultPlatform = DEFAULT_PLATFORMS.find(p => p.name === platformName)
    if (defaultPlatform) {
      const newPlatform: SocialPlatform = {
        id: `platform-${Date.now()}`,
        name: defaultPlatform.name,
        url: '',
        icon: defaultPlatform.icon,
        color: defaultPlatform.color,
        enabled: true
      }
      handleSettingsChange('platforms', [...settings.platforms, newPlatform])
    }
  }

  const removePlatform = (index: number) => {
    const newPlatforms = settings.platforms.filter((_, i) => i !== index)
    handleSettingsChange('platforms', newPlatforms)
  }

  const getSizeStyles = () => {
    const sizeMap = {
      sm: { width: '32px', height: '32px', fontSize: '14px', padding: '8px' },
      md: { width: '40px', height: '40px', fontSize: '16px', padding: '12px' },
      lg: { width: '48px', height: '48px', fontSize: '20px', padding: '16px' },
      xl: { width: '56px', height: '56px', fontSize: '24px', padding: '20px' }
    }
    return sizeMap[settings.size] || sizeMap.md
  }

  const getLayoutStyles = () => {
    const baseStyles = {
      gap: `${settings.spacing}px`
    }

    switch (settings.layout) {
      case 'horizontal':
        return {
          ...baseStyles,
          display: 'flex',
          flexDirection: 'row' as const,
          justifyContent: settings.alignment === 'center' ? 'center' : settings.alignment === 'right' ? 'flex-end' : 'flex-start'
        }
      case 'vertical':
        return {
          ...baseStyles,
          display: 'flex',
          flexDirection: 'column' as const,
          alignItems: settings.alignment === 'center' ? 'center' : settings.alignment === 'right' ? 'flex-end' : 'flex-start'
        }
      case 'grid':
        return {
          ...baseStyles,
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(40px, 1fr))',
          justifyItems: settings.alignment === 'center' ? 'center' : settings.alignment === 'right' ? 'end' : 'start'
        }
      default:
        return baseStyles
    }
  }

  const renderSocialLink = (platform: SocialPlatform, index: number) => {
    if (!platform.enabled || !platform.url) return null

    const sizeStyles = getSizeStyles()
    const backgroundColor = settings.colors.useCustom ? settings.colors.background : platform.color
    const textColor = settings.colors.useCustom ? settings.colors.text : 'white'

    const linkStyles: React.CSSProperties = {
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      textDecoration: 'none',
      borderRadius: settings.borderRadius || '8px',
      transition: `all ${settings.animation.duration || 300}ms ease`,
      cursor: 'pointer',
      ...sizeStyles
    }

    if (settings.style === 'icon') {
      linkStyles.backgroundColor = backgroundColor
      linkStyles.color = textColor
      linkStyles.width = sizeStyles.width
      linkStyles.height = sizeStyles.height
    } else if (settings.style === 'button') {
      linkStyles.backgroundColor = backgroundColor
      linkStyles.color = textColor
      linkStyles.padding = sizeStyles.padding
      linkStyles.minWidth = 'auto'
      linkStyles.height = 'auto'
    } else if (settings.style === 'text') {
      linkStyles.color = backgroundColor
      linkStyles.backgroundColor = 'transparent'
      linkStyles.padding = '4px 8px'
    } else if (settings.style === 'icon-text') {
      linkStyles.backgroundColor = backgroundColor
      linkStyles.color = textColor
      linkStyles.padding = sizeStyles.padding
      linkStyles.minWidth = 'auto'
      linkStyles.height = 'auto'
    }

    const content = () => {
      switch (settings.style) {
        case 'icon':
          return <span style={{ fontSize: sizeStyles.fontSize }}>{platform.icon}</span>
        case 'button':
          return <span style={{ fontSize: sizeStyles.fontSize }}>{platform.name}</span>
        case 'text':
          return <span style={{ fontSize: sizeStyles.fontSize }}>{platform.name}</span>
        case 'icon-text':
          return (
            <HStack spacing={2}>
              <span style={{ fontSize: sizeStyles.fontSize }}>{platform.icon}</span>
              <span style={{ fontSize: sizeStyles.fontSize }}>{platform.name}</span>
            </HStack>
          )
        default:
          return <span style={{ fontSize: sizeStyles.fontSize }}>{platform.icon}</span>
      }
    }

    return (
      <Link
        key={platform.id}
        href={platform.url}
        target={settings.target}
        style={linkStyles}
        _hover={{
          backgroundColor: settings.colors.useCustom ? settings.colors.hover.background : platform.color,
          color: settings.colors.useCustom ? settings.colors.hover.text : 'white',
          transform: settings.animation.enabled ? 
            settings.animation.type === 'scale' ? 'scale(1.1)' :
            settings.animation.type === 'bounce' ? 'translateY(-2px)' :
            settings.animation.type === 'rotate' ? 'rotate(5deg)' :
            settings.animation.type === 'pulse' ? 'scale(1.05)' : 'none' : 'none'
        }}
      >
        {content()}
      </Link>
    )
  }

  const renderSocialEditor = () => (
    <Modal isOpen={isOpen} onClose={onClose} size="xl">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>Social Links Settings</ModalHeader>
        <ModalCloseButton />
        <ModalBody pb={6}>
          <VStack spacing={6} align="stretch">
            {/* Platforms Management */}
            <Box>
              <HStack justify="space-between" mb={4}>
                <Text fontSize="md" fontWeight="semibold">Social Platforms</Text>
                <Select
                  placeholder="Add platform"
                  size="sm"
                  width="150px"
                  onChange={(e) => {
                    if (e.target.value) {
                      addPlatform(e.target.value)
                      e.target.value = ''
                    }
                  }}
                >
                  {DEFAULT_PLATFORMS
                    .filter(p => !settings.platforms.some(sp => sp.name === p.name))
                    .map(platform => (
                      <option key={platform.name} value={platform.name}>
                        {platform.icon} {platform.name}
                      </option>
                    ))}
                </Select>
              </HStack>
              
              <VStack spacing={3} align="stretch">
                {settings.platforms.map((platform, index) => (
                  <Box key={platform.id} p={3} border="1px" borderColor={borderColor} borderRadius="md">
                    <HStack justify="space-between" mb={2}>
                      <HStack>
                        <Text fontSize="sm">{platform.icon}</Text>
                        <Text fontSize="sm" fontWeight="medium">{platform.name}</Text>
                      </HStack>
                      <HStack>
                        <Switch
                          size="sm"
                          isChecked={platform.enabled}
                          onChange={(e) => handlePlatformChange(index, { enabled: e.target.checked })}
                        />
                        <IconButton
                          aria-label="Delete platform"
                          icon={<DeleteIcon />}
                          size="xs"
                          colorScheme="red"
                          variant="ghost"
                          onClick={() => removePlatform(index)}
                        />
                      </HStack>
                    </HStack>
                    
                    <FormControl>
                      <FormLabel fontSize="xs">URL</FormLabel>
                      <Input
                        value={platform.url}
                        onChange={(e) => handlePlatformChange(index, { url: e.target.value })}
                        size="sm"
                        placeholder={`https://${platform.name.toLowerCase()}.com/username`}
                      />
                    </FormControl>
                  </Box>
                ))}
              </VStack>
            </Box>

            {/* Layout Settings */}
            <Box>
              <Text fontSize="md" fontWeight="semibold" mb={3}>Layout</Text>
              <VStack spacing={3} align="stretch">
                <HStack>
                  <FormControl>
                    <FormLabel fontSize="sm">Layout</FormLabel>
                    <Select
                      value={settings.layout}
                      onChange={(e) => handleSettingsChange('layout', e.target.value)}
                      size="sm"
                    >
                      <option value="horizontal">Horizontal</option>
                      <option value="vertical">Vertical</option>
                      <option value="grid">Grid</option>
                    </Select>
                  </FormControl>
                  
                  <FormControl>
                    <FormLabel fontSize="sm">Alignment</FormLabel>
                    <Select
                      value={settings.alignment}
                      onChange={(e) => handleSettingsChange('alignment', e.target.value)}
                      size="sm"
                    >
                      <option value="left">Left</option>
                      <option value="center">Center</option>
                      <option value="right">Right</option>
                    </Select>
                  </FormControl>
                </HStack>

                <HStack>
                  <FormControl>
                    <FormLabel fontSize="sm">Size</FormLabel>
                    <Select
                      value={settings.size}
                      onChange={(e) => handleSettingsChange('size', e.target.value)}
                      size="sm"
                    >
                      <option value="sm">Small</option>
                      <option value="md">Medium</option>
                      <option value="lg">Large</option>
                      <option value="xl">Extra Large</option>
                    </Select>
                  </FormControl>
                  
                  <FormControl>
                    <FormLabel fontSize="sm">Style</FormLabel>
                    <Select
                      value={settings.style}
                      onChange={(e) => handleSettingsChange('style', e.target.value)}
                      size="sm"
                    >
                      <option value="icon">Icon Only</option>
                      <option value="button">Button</option>
                      <option value="text">Text Only</option>
                      <option value="icon-text">Icon + Text</option>
                    </Select>
                  </FormControl>
                </HStack>
              </VStack>
            </Box>

            {/* Styling */}
            <Box>
              <Text fontSize="md" fontWeight="semibold" mb={3}>Styling</Text>
              <VStack spacing={3} align="stretch">
                <FormControl display="flex" alignItems="center">
                  <FormLabel fontSize="sm" mb="0">Custom Colors</FormLabel>
                  <Switch
                    isChecked={settings.colors.useCustom}
                    onChange={(e) => handleNestedSettingsChange('colors', 'useCustom', e.target.checked)}
                  />
                </FormControl>
                
                {settings.colors.useCustom && (
                  <HStack>
                    <FormControl>
                      <FormLabel fontSize="sm">Background</FormLabel>
                      <Input
                        type="color"
                        value={settings.colors.background}
                        onChange={(e) => handleNestedSettingsChange('colors', 'background', e.target.value)}
                        size="sm"
                      />
                    </FormControl>
                    <FormControl>
                      <FormLabel fontSize="sm">Text</FormLabel>
                      <Input
                        type="color"
                        value={settings.colors.text}
                        onChange={(e) => handleNestedSettingsChange('colors', 'text', e.target.value)}
                        size="sm"
                      />
                    </FormControl>
                  </HStack>
                )}
              </VStack>
            </Box>
          </VStack>
        </ModalBody>
      </ModalContent>
    </Modal>
  )

  if (settings.platforms.filter(p => p.enabled && p.url).length === 0 && isEditing) {
    return (
      <Box
        className={className}
        style={style}
        border="2px dashed"
        borderColor="gray.300"
        borderRadius="md"
        p={8}
        textAlign="center"
        bg={bgColor}
        cursor="pointer"
        onClick={onOpen}
      >
        <VStack spacing={3}>
          <Text color="gray.500" fontSize="sm">
            Click to add social links
          </Text>
          <Button size="sm" colorScheme="blue" onClick={onOpen}>
            Add Social Links
          </Button>
        </VStack>
        {renderSocialEditor()}
      </Box>
    )
  }

  return (
    <Box className={className} style={style} position="relative">
      {isEditing && (
        <IconButton
          aria-label="Edit social links"
          icon={<SettingsIcon />}
          size="sm"
          position="absolute"
          top={-2}
          right={-2}
          zIndex={2}
          onClick={onOpen}
          colorScheme="blue"
        />
      )}

      <Box style={getLayoutStyles()}>
        {settings.platforms.map((platform, index) => renderSocialLink(platform, index))}
      </Box>

      {renderSocialEditor()}
    </Box>
  )
}

export default SocialLinks
