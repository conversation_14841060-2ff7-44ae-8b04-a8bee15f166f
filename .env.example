# Global Environment Variables Template
# Copy this to .env and fill in your actual values

# Supabase Configuration
SUPABASE_URL=https://cikzkzviubwpruiowapp.supabase.co
SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# JWT Configuration
JWT_SECRET=your_jwt_secret_here

# Vercel Configuration
VERCEL_TOKEN=************************
VERCEL_PROJECT_ID=prj_eW86m1NmWx2W3IAZGcHWL12Lz0HA

# Stripe Configuration (when available)
STRIPE_SECRET_KEY=your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret_here

# Email Configuration
EMAIL_FROM=<EMAIL>
SENDGRID_API_KEY=your_sendgrid_api_key_here

# Domain Configuration
DOMAIN_REGISTRAR_API_KEY=your_domain_registrar_api_key_here

# Analytics Configuration
GOOGLE_ANALYTICS_ID=your_ga_id_here

# File Upload Configuration
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp,application/pdf

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,https://yourdomain.com

# Node Environment
NODE_ENV=development
PORT=3000

