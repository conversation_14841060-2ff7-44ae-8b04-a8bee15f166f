'use client'

import {
  Box,
  <PERSON>ton,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  MenuDivider,
  HStack,
  Text,
  Badge,
  Icon,
  useColorModeValue,
  useToast
} from '@chakra-ui/react'
import {
  ChevronDownIcon,
  AddIcon,
  EditIcon,
  DeleteIcon,
  CheckIcon
} from '@chakra-ui/icons'
import { useLanguage, useTranslation } from '@/lib/contexts/LanguageContext'

interface LanguageSwitcherProps {
  type: 'ui' | 'content'
}

export function LanguageSwitcher({ type }: LanguageSwitcherProps) {
  const toast = useToast()
  const { t } = useTranslation()

  const {
    uiLanguage,
    setUILanguage,
    contentLanguages,
    currentContentLanguage,
    setCurrentContentLanguage,
    addContentLanguage,
    removeContentLanguage,
    availableLanguages
  } = useLanguage()

  const bgColor = useColorModeValue('white', 'gray.800')
  const borderColor = useColorModeValue('gray.200', 'gray.600')

  const languages = type === 'ui' ? availableLanguages : contentLanguages
  const currentLanguage = type === 'ui' ? uiLanguage : currentContentLanguage

  const getCurrentLanguageData = () => {
    return languages.find(lang => lang.code === currentLanguage) || languages[0]
  }

  const handleLanguageChange = (languageCode: string) => {
    if (type === 'ui') {
      setUILanguage(languageCode)
      const language = languages.find(l => l.code === languageCode)
      toast({
        title: t('language.ui'),
        description: `Interface language changed to ${language?.nativeName}`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      })
    } else {
      setCurrentContentLanguage(languageCode)
      const language = languages.find(l => l.code === languageCode)
      toast({
        title: t('language.content'),
        description: `Content language changed to ${language?.nativeName}`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      })
    }
  }

  const handleAddLanguage = () => {
    if (type === 'content') {
      // Add Arabic as second language for content
      const arabicLanguage = availableLanguages.find(lang => lang.code === 'ar')
      const arabicExists = contentLanguages.find(lang => lang.code === 'ar')

      if (!arabicExists && arabicLanguage) {
        addContentLanguage(arabicLanguage)

        toast({
          title: t('language.add'),
          description: 'Arabic language has been added to your website',
          status: 'success',
          duration: 3000,
          isClosable: true,
        })
      } else {
        toast({
          title: 'Language Already Added',
          description: 'Arabic is already available for your website',
          status: 'info',
          duration: 3000,
          isClosable: true,
        })
      }
    }
  }

  const handleRemoveLanguage = (languageCode: string) => {
    if (type === 'content' && languageCode !== 'en') {
      removeContentLanguage(languageCode)

      toast({
        title: t('language.remove'),
        description: 'Language has been removed from your website',
        status: 'success',
        duration: 3000,
        isClosable: true,
      })
    }
  }

  const currentLang = getCurrentLanguageData()

  return (
    <Menu>
      <MenuButton
        as={Button}
        rightIcon={<ChevronDownIcon />}
        size="sm"
        variant="outline"
        bg={bgColor}
        borderColor={borderColor}
      >
        <HStack spacing={2}>
          <Text fontSize="sm">{currentLang.flag}</Text>
          <Text fontSize="sm">{currentLang.nativeName}</Text>
          {type === 'content' && contentLanguages.length > 1 && (
            <Badge size="sm" colorScheme="blue">
              {contentLanguages.length}
            </Badge>
          )}
        </HStack>
      </MenuButton>
      
      <MenuList>
        <Text fontSize="xs" color="gray.500" px={3} py={1} fontWeight="semibold">
          {type === 'ui' ? t('language.ui') : t('language.content')}
        </Text>
        
        {languages.map((language) => (
          <MenuItem
            key={language.code}
            onClick={() => handleLanguageChange(language.code)}
            bg={currentLanguage === language.code ? 'blue.50' : 'transparent'}
          >
            <HStack justify="space-between" w="100%">
              <HStack spacing={3}>
                <Text fontSize="sm">{language.flag}</Text>
                <Box>
                  <Text fontSize="sm" fontWeight="medium">
                    {language.nativeName}
                  </Text>
                  <Text fontSize="xs" color="gray.500">
                    {language.name}
                  </Text>
                </Box>
              </HStack>
              <HStack spacing={1}>
                {language.isDefault && (
                  <Badge size="sm" colorScheme="green">
                    Default
                  </Badge>
                )}
                {currentLanguage === language.code && (
                  <Icon as={CheckIcon} color="blue.500" boxSize={3} />
                )}
                {type === 'content' && !language.isDefault && (
                  <Button
                    size="xs"
                    variant="ghost"
                    colorScheme="red"
                    onClick={(e) => {
                      e.stopPropagation()
                      handleRemoveLanguage(language.code)
                    }}
                  >
                    <DeleteIcon boxSize={3} />
                  </Button>
                )}
              </HStack>
            </HStack>
          </MenuItem>
        ))}
        
        {type === 'content' && (
          <>
            <MenuDivider />
            <MenuItem onClick={handleAddLanguage} icon={<AddIcon />}>
              <Text fontSize="sm">{t('language.add')} Arabic</Text>
            </MenuItem>
          </>
        )}
      </MenuList>
    </Menu>
  )
}
