'use client'

import { useState } from 'react'
import {
  Box,
  Button,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  MenuDivider,
  HStack,
  Text,
  Badge,
  Icon,
  useColorModeValue,
  useToast
} from '@chakra-ui/react'
import {
  ChevronDownIcon,
  AddIcon,
  EditIcon,
  DeleteIcon,
  CheckIcon
} from '@chakra-ui/icons'

interface Language {
  code: string
  name: string
  nativeName: string
  flag: string
  isDefault: boolean
  isActive: boolean
}

interface LanguageSwitcherProps {
  type: 'ui' | 'content'
}

export function LanguageSwitcher({ type }: LanguageSwitcherProps) {
  const toast = useToast()
  
  // UI Languages (for interface)
  const [uiLanguages] = useState<Language[]>([
    {
      code: 'en',
      name: 'English',
      nativeName: 'English',
      flag: '🇺🇸',
      isDefault: true,
      isActive: true
    },
    {
      code: 'ar',
      name: 'Arabic',
      nativeName: 'العربية',
      flag: '🇸🇦',
      isDefault: false,
      isActive: false
    }
  ])

  // Content Languages (for website content)
  const [contentLanguages, setContentLanguages] = useState<Language[]>([
    {
      code: 'en',
      name: 'English',
      nativeName: 'English',
      flag: '🇺🇸',
      isDefault: true,
      isActive: true
    }
  ])

  const [currentUILanguage, setCurrentUILanguage] = useState('en')
  const [currentContentLanguage, setCurrentContentLanguage] = useState('en')

  const bgColor = useColorModeValue('white', 'gray.800')
  const borderColor = useColorModeValue('gray.200', 'gray.600')

  const languages = type === 'ui' ? uiLanguages : contentLanguages
  const currentLanguage = type === 'ui' ? currentUILanguage : currentContentLanguage

  const getCurrentLanguageData = () => {
    return languages.find(lang => lang.code === currentLanguage) || languages[0]
  }

  const handleLanguageChange = (languageCode: string) => {
    if (type === 'ui') {
      setCurrentUILanguage(languageCode)
      // TODO: Implement UI language change logic
      toast({
        title: 'UI Language Changed',
        description: `Interface language changed to ${languages.find(l => l.code === languageCode)?.name}`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      })
    } else {
      setCurrentContentLanguage(languageCode)
      // TODO: Implement content language change logic
      toast({
        title: 'Content Language Changed',
        description: `Content language changed to ${languages.find(l => l.code === languageCode)?.name}`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      })
    }
  }

  const handleAddLanguage = () => {
    if (type === 'content') {
      // Add Arabic as second language for content
      const arabicExists = contentLanguages.find(lang => lang.code === 'ar')
      if (!arabicExists) {
        setContentLanguages(prev => [...prev, {
          code: 'ar',
          name: 'Arabic',
          nativeName: 'العربية',
          flag: '🇸🇦',
          isDefault: false,
          isActive: false
        }])
        
        toast({
          title: 'Language Added',
          description: 'Arabic language has been added to your website',
          status: 'success',
          duration: 3000,
          isClosable: true,
        })
      } else {
        toast({
          title: 'Language Already Added',
          description: 'Arabic is already available for your website',
          status: 'info',
          duration: 3000,
          isClosable: true,
        })
      }
    }
  }

  const handleRemoveLanguage = (languageCode: string) => {
    if (type === 'content' && languageCode !== 'en') {
      setContentLanguages(prev => prev.filter(lang => lang.code !== languageCode))
      
      if (currentContentLanguage === languageCode) {
        setCurrentContentLanguage('en')
      }
      
      toast({
        title: 'Language Removed',
        description: 'Language has been removed from your website',
        status: 'success',
        duration: 3000,
        isClosable: true,
      })
    }
  }

  const currentLang = getCurrentLanguageData()

  return (
    <Menu>
      <MenuButton
        as={Button}
        rightIcon={<ChevronDownIcon />}
        size="sm"
        variant="outline"
        bg={bgColor}
        borderColor={borderColor}
      >
        <HStack spacing={2}>
          <Text fontSize="sm">{currentLang.flag}</Text>
          <Text fontSize="sm">{currentLang.nativeName}</Text>
          {type === 'content' && contentLanguages.length > 1 && (
            <Badge size="sm" colorScheme="blue">
              {contentLanguages.length}
            </Badge>
          )}
        </HStack>
      </MenuButton>
      
      <MenuList>
        <Text fontSize="xs" color="gray.500" px={3} py={1} fontWeight="semibold">
          {type === 'ui' ? 'Interface Language' : 'Content Language'}
        </Text>
        
        {languages.map((language) => (
          <MenuItem
            key={language.code}
            onClick={() => handleLanguageChange(language.code)}
            bg={currentLanguage === language.code ? 'blue.50' : 'transparent'}
          >
            <HStack justify="space-between" w="100%">
              <HStack spacing={3}>
                <Text fontSize="sm">{language.flag}</Text>
                <Box>
                  <Text fontSize="sm" fontWeight="medium">
                    {language.nativeName}
                  </Text>
                  <Text fontSize="xs" color="gray.500">
                    {language.name}
                  </Text>
                </Box>
              </HStack>
              <HStack spacing={1}>
                {language.isDefault && (
                  <Badge size="sm" colorScheme="green">
                    Default
                  </Badge>
                )}
                {currentLanguage === language.code && (
                  <Icon as={CheckIcon} color="blue.500" boxSize={3} />
                )}
                {type === 'content' && !language.isDefault && (
                  <Button
                    size="xs"
                    variant="ghost"
                    colorScheme="red"
                    onClick={(e) => {
                      e.stopPropagation()
                      handleRemoveLanguage(language.code)
                    }}
                  >
                    <DeleteIcon boxSize={3} />
                  </Button>
                )}
              </HStack>
            </HStack>
          </MenuItem>
        ))}
        
        {type === 'content' && (
          <>
            <MenuDivider />
            <MenuItem onClick={handleAddLanguage} icon={<AddIcon />}>
              <Text fontSize="sm">Add Arabic Language</Text>
            </MenuItem>
          </>
        )}
      </MenuList>
    </Menu>
  )
}
