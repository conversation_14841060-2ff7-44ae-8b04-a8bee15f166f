'use client'

import React, { createContext, useContext, useState, useEffect } from 'react'

export interface Language {
  code: string
  name: string
  nativeName: string
  flag: string
  direction: 'ltr' | 'rtl'
  isDefault: boolean
}

export interface LanguageContextType {
  // UI Language (Interface)
  uiLanguage: string
  setUILanguage: (code: string) => void
  uiDirection: 'ltr' | 'rtl'
  
  // Content Languages (Website content)
  contentLanguages: Language[]
  currentContentLanguage: string
  setCurrentContentLanguage: (code: string) => void
  addContentLanguage: (language: Language) => void
  removeContentLanguage: (code: string) => void
  
  // Available languages
  availableLanguages: Language[]
}

const availableLanguages: Language[] = [
  {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸',
    direction: 'ltr',
    isDefault: true
  },
  {
    code: 'ar',
    name: 'Arabic',
    nativeName: 'العربية',
    flag: '🇸🇦',
    direction: 'rtl',
    isDefault: false
  }
]

const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [uiLanguage, setUILanguageState] = useState('en')
  const [contentLanguages, setContentLanguages] = useState<Language[]>([
    availableLanguages.find(lang => lang.code === 'en')!
  ])
  const [currentContentLanguage, setCurrentContentLanguage] = useState('en')

  // Get UI direction based on current UI language
  const uiDirection = availableLanguages.find(lang => lang.code === uiLanguage)?.direction || 'ltr'

  // Update document direction when UI language changes
  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.documentElement.dir = uiDirection
      document.documentElement.lang = uiLanguage
    }
  }, [uiLanguage, uiDirection])

  const setUILanguage = (code: string) => {
    setUILanguageState(code)
    // Store in localStorage for persistence
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem('ui-language', code)
    }
  }

  const addContentLanguage = (language: Language) => {
    if (!contentLanguages.find(lang => lang.code === language.code)) {
      setContentLanguages(prev => [...prev, language])
    }
  }

  const removeContentLanguage = (code: string) => {
    if (code !== 'en') { // Don't allow removing English (default)
      setContentLanguages(prev => prev.filter(lang => lang.code !== code))
      if (currentContentLanguage === code) {
        setCurrentContentLanguage('en')
      }
    }
  }

  // Load saved UI language on mount
  useEffect(() => {
    if (typeof localStorage !== 'undefined') {
      const savedUILanguage = localStorage.getItem('ui-language')
      if (savedUILanguage && availableLanguages.find(lang => lang.code === savedUILanguage)) {
        setUILanguageState(savedUILanguage)
      }
    }
  }, [])

  const value: LanguageContextType = {
    uiLanguage,
    setUILanguage,
    uiDirection,
    contentLanguages,
    currentContentLanguage,
    setCurrentContentLanguage,
    addContentLanguage,
    removeContentLanguage,
    availableLanguages
  }

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  )
}

export function useLanguage() {
  const context = useContext(LanguageContext)
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider')
  }
  return context
}

// Translation hook (basic implementation)
export function useTranslation() {
  const { uiLanguage } = useLanguage()
  
  const translations: Record<string, Record<string, string>> = {
    en: {
      'editor.save': 'Save',
      'editor.publish': 'Publish',
      'editor.preview': 'Preview',
      'editor.undo': 'Undo',
      'editor.redo': 'Redo',
      'editor.sections': 'Sections',
      'editor.elements': 'Elements',
      'editor.templates': 'Templates',
      'editor.properties': 'Properties',
      'editor.layers': 'Layers',
      'language.ui': 'Interface Language',
      'language.content': 'Content Language',
      'language.add': 'Add Language',
      'language.remove': 'Remove Language',
      'page.new': 'New Page',
      'page.duplicate': 'Duplicate Page',
      'page.delete': 'Delete Page',
      'page.settings': 'Page Settings',
      'website.colors': 'Color Palette',
      'website.fonts': 'Font Family',
      'website.buttons': 'Button Styles',
      'website.logo': 'Logo',
      'website.favicon': 'Favicon'
    },
    ar: {
      'editor.save': 'حفظ',
      'editor.publish': 'نشر',
      'editor.preview': 'معاينة',
      'editor.undo': 'تراجع',
      'editor.redo': 'إعادة',
      'editor.sections': 'الأقسام',
      'editor.elements': 'العناصر',
      'editor.templates': 'القوالب',
      'editor.properties': 'الخصائص',
      'editor.layers': 'الطبقات',
      'language.ui': 'لغة الواجهة',
      'language.content': 'لغة المحتوى',
      'language.add': 'إضافة لغة',
      'language.remove': 'إزالة لغة',
      'page.new': 'صفحة جديدة',
      'page.duplicate': 'نسخ الصفحة',
      'page.delete': 'حذف الصفحة',
      'page.settings': 'إعدادات الصفحة',
      'website.colors': 'لوحة الألوان',
      'website.fonts': 'نوع الخط',
      'website.buttons': 'أنماط الأزرار',
      'website.logo': 'الشعار',
      'website.favicon': 'أيقونة الموقع'
    }
  }
  
  const t = (key: string): string => {
    return translations[uiLanguage]?.[key] || translations.en[key] || key
  }
  
  return { t }
}
