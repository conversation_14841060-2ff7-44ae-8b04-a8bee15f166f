'use client'

import {
  Box,
  VStack,
  Text,
  Icon,
  useColorModeValue,
  SimpleGrid,
  Tooltip
} from '@chakra-ui/react'
import {
  EditIcon,
  ViewIcon,
  LinkIcon,
  PhoneIcon,
  EmailIcon,
  CalendarIcon,
  StarIcon,
  AttachmentIcon
} from '@chakra-ui/icons'
import { motion } from 'framer-motion'

interface ElementTemplate {
  id: string
  type: 'text' | 'image' | 'video' | 'button' | 'form' | 'map' | 'social' | 'slideshow' | 'navmenu' | 'icon' | 'custom'
  name: string
  description: string
  icon: any
  defaultProps: Record<string, any>
  defaultStyle: Record<string, any>
}

// Element templates based on Old Builder analysis
const elementTemplates: ElementTemplate[] = [
  {
    id: 'text-heading',
    type: 'text',
    name: 'Heading',
    description: 'Large heading text with rich editing',
    icon: EditIcon,
    defaultProps: {
      richText: true,
      content: '<h1>Your Heading Here</h1>',
      fontSize: '32px',
      fontWeight: 'bold',
      color: '#000000',
      textAlign: 'left'
    },
    defaultStyle: {
      marginBottom: '16px'
    }
  },
  {
    id: 'text-paragraph',
    type: 'text',
    name: 'Rich Text',
    description: 'Rich text with formatting controls',
    icon: EditIcon,
    defaultProps: {
      richText: true,
      content: '<p>Your paragraph text goes here. You can edit this content with rich formatting options including <strong>bold</strong>, <em>italic</em>, and <u>underline</u>.</p>',
      fontSize: '16px',
      lineHeight: '1.6',
      color: '#000000',
      textAlign: 'left'
    },
    defaultStyle: {
      marginBottom: '16px'
    }
  },
  {
    id: 'image-basic',
    type: 'image',
    name: 'Image',
    description: 'Responsive image',
    icon: AttachmentIcon,
    defaultProps: {
      src: '/placeholder-image.jpg',
      alt: 'Image description',
      width: '100%',
      height: 'auto'
    },
    defaultStyle: {
      borderRadius: '8px'
    }
  },
  {
    id: 'button-primary',
    type: 'button',
    name: 'Button',
    description: 'Call-to-action button',
    icon: LinkIcon,
    defaultProps: {
      text: 'Click Me',
      colorScheme: 'blue',
      size: 'md'
    },
    defaultStyle: {}
  },
  {
    id: 'video-embed',
    type: 'video',
    name: 'Video',
    description: 'Video player',
    icon: AttachmentIcon,
    defaultProps: {
      src: '/placeholder-video.mp4',
      controls: true,
      width: '100%'
    },
    defaultStyle: {
      borderRadius: '8px'
    }
  },
  {
    id: 'form-contact',
    type: 'form',
    name: 'Contact Form',
    description: 'Contact form with fields',
    icon: EmailIcon,
    defaultProps: {
      formType: 'contact',
      fields: ['name', 'email', 'message'],
      submitText: 'Send Message'
    },
    defaultStyle: {}
  },
  {
    id: 'map-location',
    type: 'map',
    name: 'Map',
    description: 'Interactive map',
    icon: CalendarIcon,
    defaultProps: {
      location: 'New York, NY',
      zoom: 12,
      height: '300px'
    },
    defaultStyle: {
      borderRadius: '8px'
    }
  },
  {
    id: 'social-links',
    type: 'social',
    name: 'Social Links',
    description: 'Social media buttons with customization',
    icon: StarIcon,
    defaultProps: {
      platforms: [
        {
          id: 'facebook',
          name: 'Facebook',
          url: 'https://facebook.com/yourpage',
          icon: '📘',
          color: '#1877F2',
          enabled: true
        },
        {
          id: 'twitter',
          name: 'Twitter',
          url: 'https://twitter.com/yourusername',
          icon: '🐦',
          color: '#1DA1F2',
          enabled: true
        },
        {
          id: 'instagram',
          name: 'Instagram',
          url: 'https://instagram.com/yourusername',
          icon: '📷',
          color: '#E4405F',
          enabled: true
        }
      ],
      layout: 'horizontal',
      alignment: 'center',
      size: 'md',
      linkStyle: 'icon'
    },
    defaultStyle: {}
  },
  {
    id: 'slideshow-gallery',
    type: 'slideshow',
    name: 'Slideshow',
    description: 'Image/video carousel with controls',
    icon: CalendarIcon,
    defaultProps: {
      slides: [
        {
          id: 'slide1',
          type: 'image',
          src: '/placeholder-slide1.jpg',
          alt: 'Slide 1',
          caption: 'First slide caption'
        },
        {
          id: 'slide2',
          type: 'image',
          src: '/placeholder-slide2.jpg',
          alt: 'Slide 2',
          caption: 'Second slide caption'
        },
        {
          id: 'slide3',
          type: 'image',
          src: '/placeholder-slide3.jpg',
          alt: 'Slide 3',
          caption: 'Third slide caption'
        }
      ],
      autoplay: true,
      autoplaySpeed: 3,
      showArrows: true,
      showDots: true,
      aspectRatio: '16:9'
    },
    defaultStyle: {
      borderRadius: '8px'
    }
  },
  {
    id: 'navmenu-horizontal',
    type: 'navmenu',
    name: 'Navigation Menu',
    description: 'Responsive navigation menu with mobile support',
    icon: LinkIcon,
    defaultProps: {
      items: [
        { id: '1', label: 'Home', url: '/', target: '_self' },
        { id: '2', label: 'About', url: '/about', target: '_self' },
        { id: '3', label: 'Services', url: '/services', target: '_self' },
        { id: '4', label: 'Contact', url: '/contact', target: '_self' }
      ],
      layout: 'horizontal',
      alignment: 'left',
      style: {
        backgroundColor: 'transparent',
        textColor: '#333333',
        hoverColor: '#0066cc',
        fontSize: 16,
        fontWeight: 'normal',
        padding: 12,
        borderRadius: 0
      },
      mobile: {
        enabled: true,
        breakpoint: 768,
        hamburgerColor: '#333333'
      }
    },
    defaultStyle: {}
  },
  {
    id: 'icon-general',
    type: 'icon',
    name: 'Icon',
    description: 'Customizable icon with animations and links',
    icon: StarIcon,
    defaultProps: {
      name: 'home',
      collection: 'general',
      size: 24,
      color: '#333333',
      style: {
        backgroundColor: 'transparent',
        borderRadius: 0,
        padding: 8,
        border: 'none',
        shadow: 'none'
      },
      animation: {
        enabled: false,
        type: 'none',
        duration: 1
      },
      link: {
        enabled: false,
        url: '',
        target: '_self'
      }
    },
    defaultStyle: {}
  }
]

export function ElementPalette() {
  const borderColor = useColorModeValue('gray.200', 'gray.700')
  const hoverBg = useColorModeValue('gray.50', 'gray.700')
  const iconColor = useColorModeValue('gray.600', 'gray.400')

  const handleDragStart = (e: React.DragEvent, template: ElementTemplate) => {
    e.dataTransfer.setData('application/json', JSON.stringify({
      type: 'element',
      elementType: template.type,
      name: template.name,
      defaultProps: template.defaultProps,
      defaultStyle: template.defaultStyle
    }))
  }

  return (
    <VStack spacing={0} align="stretch" p={3}>
      <Text fontSize="sm" fontWeight="semibold" color="gray.600" mb={3}>
        Drag elements to sections
      </Text>
      
      <SimpleGrid columns={2} spacing={2}>
        {elementTemplates.map((template) => (
          <Tooltip
            key={template.id}
            label={template.description}
            placement="top"
            hasArrow
          >
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Box
                p={3}
                border="1px"
                borderColor={borderColor}
                borderRadius="md"
                cursor="grab"
                _hover={{ bg: hoverBg }}
                _active={{ cursor: 'grabbing' }}
                textAlign="center"
                draggable
                onDragStart={(e) => handleDragStart(e, template)}
                minH="80px"
                display="flex"
                flexDirection="column"
                alignItems="center"
                justifyContent="center"
                gap={2}
              >
                <Icon
                  as={template.icon}
                  boxSize={5}
                  color={iconColor}
                />
                <Text fontSize="xs" fontWeight="medium" noOfLines={2}>
                  {template.name}
                </Text>
              </Box>
            </motion.div>
          </Tooltip>
        ))}
      </SimpleGrid>
    </VStack>
  )
}
