'use client'

import {
  Box,
  Flex,
  IconButton,
  Button,
  Text,
  Divider,
  useColorModeValue,
  Tooltip,
  ButtonGroup,
  Menu,
  MenuButton,
  MenuList,
  MenuItem
} from '@chakra-ui/react'
import {
  ArrowBackIcon,
  RepeatIcon,
  ViewIcon,
  DownloadIcon,
  SettingsIcon,
  ChevronDownIcon,
  TimeIcon
} from '@chakra-ui/icons'
import { useEditorStore } from '@/lib/stores/editorStore'

interface EditorToolbarProps {
  onPreview: () => void
  onToggleHistory?: () => void
}

export function EditorToolbar({ onPreview, onToggleHistory }: EditorToolbarProps) {
  const borderColor = useColorModeValue('gray.200', 'gray.700')
  const bgColor = useColorModeValue('white', 'gray.800')
  
  const {
    currentPage,
    currentBreakpoint,
    setBreakpoint,
    undo,
    redo,
    undoStack,
    redoStack
  } = useEditorStore()

  const breakpointIcons = {
    desktop: '🖥️',
    tablet: '📱',
    mobile: '📱'
  }

  const handleSave = () => {
    // TODO: Implement save functionality
    console.log('Saving page...', currentPage)
  }

  const handlePublish = () => {
    // TODO: Implement publish functionality
    console.log('Publishing page...', currentPage)
  }

  return (
    <Box
      h="60px"
      bg={bgColor}
      borderBottom="1px"
      borderColor={borderColor}
      px={4}
    >
      <Flex align="center" justify="space-between" h="100%">
        {/* Left Section */}
        <Flex align="center" gap={3}>
          <Tooltip label="Back to Dashboard">
            <IconButton
              aria-label="Back"
              icon={<ArrowBackIcon />}
              variant="ghost"
              size="sm"
            />
          </Tooltip>
          
          <Divider orientation="vertical" h="24px" />
          
          <Text fontSize="sm" fontWeight="medium" color="gray.700">
            {currentPage?.name || 'Untitled Page'}
          </Text>
        </Flex>

        {/* Center Section - Breakpoint Controls */}
        <Flex align="center" gap={2}>
          <Text fontSize="xs" color="gray.500" mr={2}>
            Device:
          </Text>
          
          <ButtonGroup size="sm" isAttached variant="outline">
            <Button
              leftIcon={<span>🖥️</span>}
              colorScheme={currentBreakpoint === 'desktop' ? 'blue' : 'gray'}
              onClick={() => setBreakpoint('desktop')}
              fontSize="xs"
            >
              Desktop
            </Button>
            <Button
              leftIcon={<span>📱</span>}
              colorScheme={currentBreakpoint === 'tablet' ? 'blue' : 'gray'}
              onClick={() => setBreakpoint('tablet')}
              fontSize="xs"
            >
              Tablet
            </Button>
            <Button
              leftIcon={<span>📱</span>}
              colorScheme={currentBreakpoint === 'mobile' ? 'blue' : 'gray'}
              onClick={() => setBreakpoint('mobile')}
              fontSize="xs"
            >
              Mobile
            </Button>
          </ButtonGroup>
        </Flex>

        {/* Right Section */}
        <Flex align="center" gap={2}>
          {/* Undo/Redo */}
          <ButtonGroup size="sm" variant="ghost">
            <Tooltip label="Undo">
              <IconButton
                aria-label="Undo"
                icon={<ArrowBackIcon transform="scaleX(-1)" />}
                onClick={undo}
                isDisabled={undoStack.length === 0}
              />
            </Tooltip>
            <Tooltip label="Redo">
              <IconButton
                aria-label="Redo"
                icon={<RepeatIcon />}
                onClick={redo}
                isDisabled={redoStack.length === 0}
              />
            </Tooltip>
          </ButtonGroup>
          
          <Divider orientation="vertical" h="24px" />
          
          {/* Preview */}
          <Tooltip label="Preview">
            <IconButton
              aria-label="Preview"
              icon={<ViewIcon />}
              variant="ghost"
              size="sm"
              onClick={onPreview}
            />
          </Tooltip>

          {/* History Panel Toggle */}
          {onToggleHistory && (
            <Tooltip label="Toggle History Panel">
              <IconButton
                aria-label="History"
                icon={<TimeIcon />}
                variant="ghost"
                size="sm"
                onClick={onToggleHistory}
              />
            </Tooltip>
          )}
          
          {/* Settings */}
          <Menu>
            <MenuButton
              as={IconButton}
              aria-label="Settings"
              icon={<SettingsIcon />}
              variant="ghost"
              size="sm"
            />
            <MenuList>
              <MenuItem onClick={handleSave}>
                <DownloadIcon mr={2} />
                Save Page
              </MenuItem>
              <MenuItem onClick={handlePublish}>
                <ViewIcon mr={2} />
                Publish Page
              </MenuItem>
            </MenuList>
          </Menu>
          
          <Divider orientation="vertical" h="24px" />
          
          {/* Save & Publish */}
          <ButtonGroup size="sm">
            <Button variant="outline" onClick={handleSave}>
              Save
            </Button>
            <Button colorScheme="blue" onClick={handlePublish}>
              Publish
            </Button>
          </ButtonGroup>
        </Flex>
      </Flex>
    </Box>
  )
}
