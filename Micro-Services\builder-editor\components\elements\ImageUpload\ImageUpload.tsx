'use client'

import React, { useState, useRef } from 'react'
import {
  Box,
  Button,
  VStack,
  HStack,
  Text,
  Input,
  Image,
  IconButton,
  useColorModeValue,
  useToast,
  Progress,
  Flex,
  Tooltip
} from '@chakra-ui/react'
import {
  AddIcon,
  DeleteIcon,
  EditIcon,
  ExternalLinkIcon,
  UploadIcon
} from '@chakra-ui/icons'

export interface ImageUploadSettings {
  src: string
  alt: string
  width?: string
  height?: string
  objectFit: 'cover' | 'contain' | 'fill' | 'scale-down' | 'none'
  borderRadius?: string
  border?: string
  shadow?: string
}

interface ImageUploadProps {
  settings: ImageUploadSettings
  onChange?: (settings: ImageUploadSettings) => void
  isEditing?: boolean
  placeholder?: string
  className?: string
  style?: React.CSSProperties
}

export function ImageUpload({
  settings,
  onChange,
  isEditing = false,
  placeholder = 'Click to add image...',
  className,
  style
}: ImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [showUrlInput, setShowUrlInput] = useState(false)
  const [urlInput, setUrlInput] = useState('')
  const fileInputRef = useRef<HTMLInputElement>(null)
  const toast = useToast()
  
  const borderColor = useColorModeValue('gray.200', 'gray.700')
  const bgColor = useColorModeValue('gray.50', 'gray.800')

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast({
        title: 'Invalid file type',
        description: 'Please select an image file',
        status: 'error',
        duration: 3000,
        isClosable: true,
      })
      return
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: 'File too large',
        description: 'Please select an image smaller than 5MB',
        status: 'error',
        duration: 3000,
        isClosable: true,
      })
      return
    }

    setIsUploading(true)
    setUploadProgress(0)

    try {
      // Create FormData for file upload
      const formData = new FormData()
      formData.append('file', file)

      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval)
            return 90
          }
          return prev + 10
        })
      }, 100)

      // Upload to media service
      const response = await fetch('/api/media/upload', {
        method: 'POST',
        body: formData,
      })

      clearInterval(progressInterval)
      setUploadProgress(100)

      if (!response.ok) {
        throw new Error('Upload failed')
      }

      const result = await response.json()
      
      // Update settings with uploaded image URL
      onChange?.({
        ...settings,
        src: result.url,
        alt: result.filename || 'Uploaded image'
      })

      toast({
        title: 'Image uploaded',
        description: 'Your image has been uploaded successfully',
        status: 'success',
        duration: 3000,
        isClosable: true,
      })

    } catch (error) {
      console.error('Upload failed:', error)
      toast({
        title: 'Upload failed',
        description: 'Failed to upload image. Please try again.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      })
    } finally {
      setIsUploading(false)
      setUploadProgress(0)
    }
  }

  const handleUrlSubmit = () => {
    if (urlInput.trim()) {
      onChange?.({
        ...settings,
        src: urlInput.trim(),
        alt: settings.alt || 'Image from URL'
      })
      setUrlInput('')
      setShowUrlInput(false)
      
      toast({
        title: 'Image added',
        description: 'Image URL has been added',
        status: 'success',
        duration: 2000,
        isClosable: true,
      })
    }
  }

  const handleRemoveImage = () => {
    onChange?.({
      ...settings,
      src: '',
      alt: ''
    })
  }

  const handleSettingsChange = (key: keyof ImageUploadSettings, value: any) => {
    onChange?.({
      ...settings,
      [key]: value
    })
  }

  const getImageStyle = (): React.CSSProperties => ({
    width: settings.width || '100%',
    height: settings.height || 'auto',
    objectFit: settings.objectFit || 'cover',
    borderRadius: settings.borderRadius || '0',
    border: settings.border || 'none',
    boxShadow: settings.shadow || 'none',
    ...style
  })

  // If image exists, show it
  if (settings.src) {
    return (
      <Box className={className} position="relative">
        <Image
          src={settings.src}
          alt={settings.alt}
          style={getImageStyle()}
          fallback={
            <Flex
              w="100%"
              h="200px"
              bg="gray.100"
              align="center"
              justify="center"
              borderRadius={settings.borderRadius || '0'}
            >
              <Text color="gray.500">Failed to load image</Text>
            </Flex>
          }
        />
        
        {isEditing && (
          <HStack
            position="absolute"
            top="2"
            right="2"
            spacing={1}
            bg="blackAlpha.700"
            borderRadius="md"
            p={1}
          >
            <Tooltip label="Change image">
              <IconButton
                aria-label="Change image"
                icon={<EditIcon />}
                size="xs"
                colorScheme="whiteAlpha"
                onClick={() => fileInputRef.current?.click()}
              />
            </Tooltip>
            <Tooltip label="Remove image">
              <IconButton
                aria-label="Remove image"
                icon={<DeleteIcon />}
                size="xs"
                colorScheme="red"
                onClick={handleRemoveImage}
              />
            </Tooltip>
          </HStack>
        )}
      </Box>
    )
  }

  // If no image and editing, show upload interface
  if (isEditing) {
    return (
      <Box className={className}>
        <VStack
          spacing={4}
          p={6}
          border="2px dashed"
          borderColor={borderColor}
          borderRadius="md"
          bg={bgColor}
          minH="200px"
          justify="center"
        >
          {isUploading ? (
            <VStack spacing={3}>
              <Text fontSize="sm" color="gray.600">
                Uploading image...
              </Text>
              <Progress value={uploadProgress} w="200px" colorScheme="blue" />
              <Text fontSize="xs" color="gray.500">
                {uploadProgress}%
              </Text>
            </VStack>
          ) : (
            <>
              <Text fontSize="sm" color="gray.600" textAlign="center">
                {placeholder}
              </Text>
              
              <VStack spacing={2}>
                <Button
                  leftIcon={<UploadIcon />}
                  size="sm"
                  colorScheme="blue"
                  onClick={() => fileInputRef.current?.click()}
                >
                  Upload Image
                </Button>
                
                <Button
                  leftIcon={<ExternalLinkIcon />}
                  size="sm"
                  variant="outline"
                  onClick={() => setShowUrlInput(!showUrlInput)}
                >
                  Add from URL
                </Button>
              </VStack>

              {showUrlInput && (
                <HStack w="100%" maxW="300px">
                  <Input
                    placeholder="Enter image URL"
                    value={urlInput}
                    onChange={(e) => setUrlInput(e.target.value)}
                    size="sm"
                  />
                  <Button size="sm" onClick={handleUrlSubmit}>
                    Add
                  </Button>
                </HStack>
              )}
            </>
          )}
        </VStack>

        <Input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileSelect}
          display="none"
        />
      </Box>
    )
  }

  // Default placeholder when not editing
  return (
    <Box
      className={className}
      w="100%"
      h="200px"
      bg="gray.100"
      display="flex"
      alignItems="center"
      justifyContent="center"
      borderRadius="md"
    >
      <Text color="gray.500" fontSize="sm">
        {placeholder}
      </Text>
    </Box>
  )
}

export default ImageUpload
