'use client'

import React, { useState, useEffect } from 'react'
import {
  Box,
  VStack,
  HStack,
  Text,
  Input,
  Select,
  Button,
  FormControl,
  FormLabel,
  Switch,
  Slider,
  SliderTrack,
  SliderFilledTrack,
  SliderThumb,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  useColorModeValue,
  IconButton,
  Image,
  Flex,
  AspectRatio
} from '@chakra-ui/react'
import { 
  ChevronLeftIcon, 
  ChevronRightIcon, 
  SettingsIcon, 
  AddIcon, 
  DeleteIcon,
  DragHandleIcon 
} from '@chakra-ui/icons'

export interface SlideItem {
  id: string
  type: 'image' | 'video'
  src: string
  alt?: string
  caption?: string
  link?: string
  poster?: string // for videos
}

export interface SlideShowSettings {
  slides: SlideItem[]
  autoplay: boolean
  autoplaySpeed: number // in seconds
  showArrows: boolean
  showDots: boolean
  infinite: boolean
  slidesToShow: number
  slidesToScroll: number
  aspectRatio: '16:9' | '4:3' | '1:1' | '3:2' | 'custom'
  height: string
  borderRadius: string
  shadow: {
    enabled: boolean
    x: number
    y: number
    blur: number
    spread: number
    color: string
  }
  transition: {
    type: 'slide' | 'fade' | 'zoom'
    duration: number // in ms
    easing: string
  }
  controls: {
    arrowColor: string
    arrowSize: 'sm' | 'md' | 'lg'
    arrowPosition: 'inside' | 'outside'
    dotColor: string
    dotSize: 'sm' | 'md' | 'lg'
    dotPosition: 'bottom' | 'top'
  }
}

interface SlideShowProps {
  settings: SlideShowSettings
  onChange?: (settings: SlideShowSettings) => void
  isEditing?: boolean
  className?: string
  style?: React.CSSProperties
}

export function SlideShow({
  settings,
  onChange,
  isEditing = false,
  className,
  style
}: SlideShowProps) {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isPlaying, setIsPlaying] = useState(settings.autoplay)
  const { isOpen, onOpen, onClose } = useDisclosure()
  
  const borderColor = useColorModeValue('gray.200', 'gray.700')
  const bgColor = useColorModeValue('gray.50', 'gray.800')

  // Auto-play functionality
  useEffect(() => {
    if (isPlaying && settings.slides.length > 1) {
      const interval = setInterval(() => {
        setCurrentSlide(prev => 
          settings.infinite 
            ? (prev + 1) % settings.slides.length
            : prev < settings.slides.length - 1 ? prev + 1 : 0
        )
      }, settings.autoplaySpeed * 1000)

      return () => clearInterval(interval)
    }
  }, [isPlaying, settings.autoplaySpeed, settings.slides.length, settings.infinite])

  const handleSettingsChange = (key: keyof SlideShowSettings, value: any) => {
    onChange?.({
      ...settings,
      [key]: value
    })
  }

  const handleNestedSettingsChange = (
    parent: keyof SlideShowSettings,
    key: string,
    value: any
  ) => {
    onChange?.({
      ...settings,
      [parent]: {
        ...(settings[parent] as any),
        [key]: value
      }
    })
  }

  const handleSlideChange = (index: number, updates: Partial<SlideItem>) => {
    const newSlides = [...settings.slides]
    newSlides[index] = { ...newSlides[index], ...updates }
    handleSettingsChange('slides', newSlides)
  }

  const addSlide = () => {
    const newSlide: SlideItem = {
      id: `slide-${Date.now()}`,
      type: 'image',
      src: '',
      alt: '',
      caption: ''
    }
    handleSettingsChange('slides', [...settings.slides, newSlide])
  }

  const removeSlide = (index: number) => {
    const newSlides = settings.slides.filter((_, i) => i !== index)
    handleSettingsChange('slides', newSlides)
    if (currentSlide >= newSlides.length) {
      setCurrentSlide(Math.max(0, newSlides.length - 1))
    }
  }

  const nextSlide = () => {
    if (settings.infinite) {
      setCurrentSlide((prev) => (prev + 1) % settings.slides.length)
    } else if (currentSlide < settings.slides.length - 1) {
      setCurrentSlide(prev => prev + 1)
    }
  }

  const prevSlide = () => {
    if (settings.infinite) {
      setCurrentSlide((prev) => (prev - 1 + settings.slides.length) % settings.slides.length)
    } else if (currentSlide > 0) {
      setCurrentSlide(prev => prev - 1)
    }
  }

  const goToSlide = (index: number) => {
    setCurrentSlide(index)
  }

  const getContainerStyle = (): React.CSSProperties => {
    const boxShadow = settings.shadow.enabled
      ? `${settings.shadow.x}px ${settings.shadow.y}px ${settings.shadow.blur}px ${settings.shadow.spread}px ${settings.shadow.color}`
      : 'none'

    return {
      borderRadius: settings.borderRadius || '0',
      boxShadow,
      overflow: 'hidden',
      position: 'relative',
      ...style
    }
  }

  const renderSlide = (slide: SlideItem, index: number) => {
    const isActive = index === currentSlide

    if (slide.type === 'video') {
      return (
        <Box
          key={slide.id}
          position="absolute"
          top="0"
          left="0"
          width="100%"
          height="100%"
          opacity={isActive ? 1 : 0}
          transition={`opacity ${settings.transition.duration}ms ${settings.transition.easing}`}
        >
          <Box
            as="video"
            width="100%"
            height="100%"
            objectFit="cover"
            controls
            poster={slide.poster}
          >
            <source src={slide.src} type="video/mp4" />
          </Box>
          {slide.caption && (
            <Box
              position="absolute"
              bottom="0"
              left="0"
              right="0"
              bg="blackAlpha.700"
              color="white"
              p={4}
              textAlign="center"
            >
              <Text fontSize="sm">{slide.caption}</Text>
            </Box>
          )}
        </Box>
      )
    }

    return (
      <Box
        key={slide.id}
        position="absolute"
        top="0"
        left="0"
        width="100%"
        height="100%"
        opacity={isActive ? 1 : 0}
        transition={`opacity ${settings.transition.duration}ms ${settings.transition.easing}`}
      >
        <Image
          src={slide.src}
          alt={slide.alt}
          width="100%"
          height="100%"
          objectFit="cover"
        />
        {slide.caption && (
          <Box
            position="absolute"
            bottom="0"
            left="0"
            right="0"
            bg="blackAlpha.700"
            color="white"
            p={4}
            textAlign="center"
          >
            <Text fontSize="sm">{slide.caption}</Text>
          </Box>
        )}
      </Box>
    )
  }

  const renderControls = () => (
    <>
      {/* Navigation Arrows */}
      {settings.showArrows && settings.slides.length > 1 && (
        <>
          <IconButton
            aria-label="Previous slide"
            icon={<ChevronLeftIcon />}
            position="absolute"
            left={settings.controls.arrowPosition === 'inside' ? 4 : -12}
            top="50%"
            transform="translateY(-50%)"
            zIndex={2}
            onClick={prevSlide}
            size={settings.controls.arrowSize}
            colorScheme="whiteAlpha"
            bg="blackAlpha.600"
            color={settings.controls.arrowColor}
            _hover={{ bg: 'blackAlpha.800' }}
          />
          <IconButton
            aria-label="Next slide"
            icon={<ChevronRightIcon />}
            position="absolute"
            right={settings.controls.arrowPosition === 'inside' ? 4 : -12}
            top="50%"
            transform="translateY(-50%)"
            zIndex={2}
            onClick={nextSlide}
            size={settings.controls.arrowSize}
            colorScheme="whiteAlpha"
            bg="blackAlpha.600"
            color={settings.controls.arrowColor}
            _hover={{ bg: 'blackAlpha.800' }}
          />
        </>
      )}

      {/* Dots Indicator */}
      {settings.showDots && settings.slides.length > 1 && (
        <HStack
          position="absolute"
          bottom={settings.controls.dotPosition === 'bottom' ? 4 : 'auto'}
          top={settings.controls.dotPosition === 'top' ? 4 : 'auto'}
          left="50%"
          transform="translateX(-50%)"
          zIndex={2}
          spacing={2}
        >
          {settings.slides.map((_, index) => (
            <Box
              key={index}
              width={settings.controls.dotSize === 'lg' ? 4 : settings.controls.dotSize === 'md' ? 3 : 2}
              height={settings.controls.dotSize === 'lg' ? 4 : settings.controls.dotSize === 'md' ? 3 : 2}
              borderRadius="full"
              bg={index === currentSlide ? settings.controls.dotColor : 'whiteAlpha.500'}
              cursor="pointer"
              onClick={() => goToSlide(index)}
              transition="all 0.2s"
              _hover={{ transform: 'scale(1.2)' }}
            />
          ))}
        </HStack>
      )}
    </>
  )

  const renderSlideEditor = () => (
    <Modal isOpen={isOpen} onClose={onClose} size="xl">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>SlideShow Settings</ModalHeader>
        <ModalCloseButton />
        <ModalBody pb={6}>
          <VStack spacing={6} align="stretch">
            {/* Slides Management */}
            <Box>
              <HStack justify="space-between" mb={4}>
                <Text fontSize="md" fontWeight="semibold">Slides</Text>
                <Button size="sm" leftIcon={<AddIcon />} onClick={addSlide}>
                  Add Slide
                </Button>
              </HStack>
              
              <VStack spacing={3} align="stretch">
                {settings.slides.map((slide, index) => (
                  <Box key={slide.id} p={3} border="1px" borderColor={borderColor} borderRadius="md">
                    <HStack justify="space-between" mb={2}>
                      <Text fontSize="sm" fontWeight="medium">Slide {index + 1}</Text>
                      <HStack>
                        <IconButton
                          aria-label="Delete slide"
                          icon={<DeleteIcon />}
                          size="xs"
                          colorScheme="red"
                          variant="ghost"
                          onClick={() => removeSlide(index)}
                        />
                      </HStack>
                    </HStack>
                    
                    <VStack spacing={2} align="stretch">
                      <FormControl>
                        <FormLabel fontSize="xs">Type</FormLabel>
                        <Select
                          value={slide.type}
                          onChange={(e) => handleSlideChange(index, { type: e.target.value as 'image' | 'video' })}
                          size="sm"
                        >
                          <option value="image">Image</option>
                          <option value="video">Video</option>
                        </Select>
                      </FormControl>
                      
                      <FormControl>
                        <FormLabel fontSize="xs">Source URL</FormLabel>
                        <Input
                          value={slide.src}
                          onChange={(e) => handleSlideChange(index, { src: e.target.value })}
                          size="sm"
                          placeholder={slide.type === 'image' ? 'Image URL' : 'Video URL'}
                        />
                      </FormControl>
                      
                      <FormControl>
                        <FormLabel fontSize="xs">Caption</FormLabel>
                        <Input
                          value={slide.caption || ''}
                          onChange={(e) => handleSlideChange(index, { caption: e.target.value })}
                          size="sm"
                          placeholder="Optional caption"
                        />
                      </FormControl>
                    </VStack>
                  </Box>
                ))}
              </VStack>
            </Box>

            {/* Playback Settings */}
            <Box>
              <Text fontSize="md" fontWeight="semibold" mb={3}>Playback</Text>
              <VStack spacing={3} align="stretch">
                <FormControl display="flex" alignItems="center">
                  <FormLabel fontSize="sm" mb="0">Autoplay</FormLabel>
                  <Switch
                    isChecked={settings.autoplay}
                    onChange={(e) => {
                      handleSettingsChange('autoplay', e.target.checked)
                      setIsPlaying(e.target.checked)
                    }}
                  />
                </FormControl>
                
                {settings.autoplay && (
                  <FormControl>
                    <FormLabel fontSize="sm">Speed: {settings.autoplaySpeed}s</FormLabel>
                    <Slider
                      value={settings.autoplaySpeed}
                      onChange={(val) => handleSettingsChange('autoplaySpeed', val)}
                      min={1}
                      max={10}
                      step={0.5}
                    >
                      <SliderTrack>
                        <SliderFilledTrack />
                      </SliderTrack>
                      <SliderThumb />
                    </Slider>
                  </FormControl>
                )}
                
                <FormControl display="flex" alignItems="center">
                  <FormLabel fontSize="sm" mb="0">Infinite Loop</FormLabel>
                  <Switch
                    isChecked={settings.infinite}
                    onChange={(e) => handleSettingsChange('infinite', e.target.checked)}
                  />
                </FormControl>
              </VStack>
            </Box>

            {/* Controls Settings */}
            <Box>
              <Text fontSize="md" fontWeight="semibold" mb={3}>Controls</Text>
              <VStack spacing={3} align="stretch">
                <FormControl display="flex" alignItems="center">
                  <FormLabel fontSize="sm" mb="0">Show Arrows</FormLabel>
                  <Switch
                    isChecked={settings.showArrows}
                    onChange={(e) => handleSettingsChange('showArrows', e.target.checked)}
                  />
                </FormControl>
                
                <FormControl display="flex" alignItems="center">
                  <FormLabel fontSize="sm" mb="0">Show Dots</FormLabel>
                  <Switch
                    isChecked={settings.showDots}
                    onChange={(e) => handleSettingsChange('showDots', e.target.checked)}
                  />
                </FormControl>
              </VStack>
            </Box>
          </VStack>
        </ModalBody>
      </ModalContent>
    </Modal>
  )

  if (settings.slides.length === 0 && isEditing) {
    return (
      <Box
        className={className}
        style={style}
        border="2px dashed"
        borderColor="gray.300"
        borderRadius="md"
        p={8}
        textAlign="center"
        bg={bgColor}
        cursor="pointer"
        onClick={onOpen}
      >
        <VStack spacing={3}>
          <Text color="gray.500" fontSize="sm">
            Click to create a slideshow
          </Text>
          <Button size="sm" colorScheme="blue" onClick={onOpen}>
            Add Slides
          </Button>
        </VStack>
        {renderSlideEditor()}
      </Box>
    )
  }

  const aspectRatioMap = {
    '16:9': 16 / 9,
    '4:3': 4 / 3,
    '1:1': 1,
    '3:2': 3 / 2,
    'custom': undefined
  }

  return (
    <Box className={className} style={getContainerStyle()}>
      {isEditing && (
        <IconButton
          aria-label="Edit slideshow"
          icon={<SettingsIcon />}
          size="sm"
          position="absolute"
          top={2}
          right={2}
          zIndex={3}
          onClick={onOpen}
          colorScheme="blue"
        />
      )}

      <AspectRatio 
        ratio={aspectRatioMap[settings.aspectRatio]} 
        height={settings.aspectRatio === 'custom' ? settings.height : undefined}
      >
        <Box position="relative" width="100%" height="100%">
          {settings.slides.map((slide, index) => renderSlide(slide, index))}
          {renderControls()}
        </Box>
      </AspectRatio>

      {renderSlideEditor()}
    </Box>
  )
}

export default SlideShow
