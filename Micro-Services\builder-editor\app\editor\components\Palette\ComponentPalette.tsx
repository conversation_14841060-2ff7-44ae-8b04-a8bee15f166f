'use client'

import { useState } from 'react'
import {
  Box,
  VStack,
  Text,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  useColorModeValue
} from '@chakra-ui/react'
import { SectionPalette } from './SectionPalette'
import { ElementPalette } from './ElementPalette'
import { TemplatePalette } from './TemplatePalette'

export function ComponentPalette() {
  const borderColor = useColorModeValue('gray.200', 'gray.700')
  const bgColor = useColorModeValue('white', 'gray.800')

  return (
    <Box h="100%" bg={bgColor}>
      <Tabs variant="enclosed" size="sm" h="100%">
        <TabList borderBottom="1px" borderColor={borderColor}>
          <Tab flex="1" fontSize="xs">Sections</Tab>
          <Tab flex="1" fontSize="xs">Elements</Tab>
          <Tab flex="1" fontSize="xs">Templates</Tab>
        </TabList>

        <TabPanels h="calc(100% - 40px)" overflowY="auto">
          <TabPanel p={0} h="100%">
            <SectionPalette />
          </TabPanel>
          
          <TabPanel p={0} h="100%">
            <ElementPalette />
          </TabPanel>
          
          <TabPanel p={0} h="100%">
            <TemplatePalette />
          </TabPanel>
        </TabPanels>
      </Tabs>
    </Box>
  )
}
