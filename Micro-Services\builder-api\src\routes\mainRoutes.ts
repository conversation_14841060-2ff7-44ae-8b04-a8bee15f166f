import express from 'express'
import { asyncHandler } from '../middleware/asyncHandler'

const router = express.Router()

router.get('/', asyncHandler(async (req, res) => {
  res.json({
    success: true,
    data: {
      service: 'builder-api',
      description: 'Core website builder API service',
      version: '1.0.0',
      endpoints: {
        sites: '/sites',
        pages: '/pages',
        health: '/health'
      }
    }
  })
}))

router.get('/info', asyncHandler(async (req, res) => {
  res.json({
    success: true,
    data: {
      service: 'builder-api',
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      timestamp: new Date().toISOString()
    }
  })
}))

export default router

