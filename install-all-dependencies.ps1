# Install dependencies for all microservices
Write-Host "Installing dependencies for all microservices..." -ForegroundColor Green

$services = @(
    "auth-service",
    "builder-api", 
    "builder-editor",
    "templates-service",
    "media-service",
    "publish-service",
    "domain-service",
    "billing-service",
    "analytics-service",
    "questionnaire-service",
    "invitation-service",
    "geo-service",
    "crm-integration",
    "admin-dashboard",
    "backoffice-api",
    "backoffice-dashboard",
    "site-dashboard",
    "test-service"
)

foreach ($service in $services) {
    $servicePath = "Micro-Services\$service"
    if (Test-Path $servicePath) {
        Write-Host "Installing dependencies for $service..." -ForegroundColor Yellow
        Set-Location $servicePath
        if (Test-Path "package.json") {
            npm install
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ $service dependencies installed successfully" -ForegroundColor Green
            } else {
                Write-Host "❌ Failed to install dependencies for $service" -ForegroundColor Red
            }
        } else {
            Write-Host "⚠️  No package.json found for $service" -ForegroundColor Yellow
        }
        Set-Location "..\..\"
    } else {
        Write-Host "⚠️  Service directory not found: $service" -ForegroundColor Yellow
    }
}

Write-Host "Dependency installation completed!" -ForegroundColor Green
