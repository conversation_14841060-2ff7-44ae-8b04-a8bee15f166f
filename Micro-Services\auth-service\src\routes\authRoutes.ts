import express from 'express'
import { supabaseAdmin, supabaseAnon } from '../lib/supabaseClient'
import {
  getUser<PERSON>romHeader,
  requireAuth,
  requireRole,
  generateTokens,
  verifyToken,
  createUserProfile,
  getUserProfile,
  updateUserProfile,
  loginSchema,
  registerSchema,
  updateProfileSchema
} from '../lib/authHelpers'
import { asyncHandler } from '../middleware/asyncHandler'

const router = express.Router()

// Register new user
router.post('/register', asyncHandler(async (req, res) => {
  const validatedData = registerSchema.parse(req.body)
  
  // Create user in Supabase Auth
  const { data: authData, error: authError } = await supabaseAnon.auth.signUp({
    email: validatedData.email,
    password: validatedData.password,
    options: {
      data: {
        full_name: validatedData.full_name
      }
    }
  })

  if (authError) {
    return res.status(400).json({
      success: false,
      error: {
        code: 'REGISTRATION_FAILED',
        message: authError.message
      }
    })
  }

  if (!authData.user) {
    return res.status(400).json({
      success: false,
      error: {
        code: 'REGISTRATION_FAILED',
        message: 'Failed to create user account'
      }
    })
  }

  // Create user profile in our database
  try {
    const userProfile = await createUserProfile({
      id: authData.user.id,
      email: validatedData.email,
      full_name: validatedData.full_name,
      language: validatedData.language,
      timezone: validatedData.timezone
    })

    // Generate tokens
    const tokens = generateTokens(userProfile)

    res.status(201).json({
      success: true,
      data: {
        user: userProfile,
        tokens
      },
      message: 'User registered successfully'
    })
  } catch (error) {
    // Clean up auth user if profile creation fails
    await supabaseAdmin.auth.admin.deleteUser(authData.user.id)
    
    res.status(500).json({
      success: false,
      error: {
        code: 'PROFILE_CREATION_FAILED',
        message: 'Failed to create user profile'
      }
    })
  }
}))

// Login user
router.post('/login', asyncHandler(async (req, res) => {
  const validatedData = loginSchema.parse(req.body)

  // Authenticate with Supabase
  const { data: authData, error: authError } = await supabaseAnon.auth.signInWithPassword({
    email: validatedData.email,
    password: validatedData.password
  })

  if (authError || !authData.user) {
    return res.status(401).json({
      success: false,
      error: {
        code: 'INVALID_CREDENTIALS',
        message: 'Invalid email or password'
      }
    })
  }

  // Get user profile
  const userProfile = await getUserProfile(authData.user.id)
  
  if (!userProfile) {
    return res.status(404).json({
      success: false,
      error: {
        code: 'USER_PROFILE_NOT_FOUND',
        message: 'User profile not found'
      }
    })
  }

  // Generate tokens
  const tokens = generateTokens(userProfile)

  res.json({
    success: true,
    data: {
      user: userProfile,
      tokens
    },
    message: 'Login successful'
  })
}))

// Get current user
router.get('/me', requireAuth, asyncHandler(async (req, res) => {
  const userProfile = await getUserProfile(req.user.id)
  
  if (!userProfile) {
    return res.status(404).json({
      success: false,
      error: {
        code: 'USER_NOT_FOUND',
        message: 'User profile not found'
      }
    })
  }

  res.json({
    success: true,
    data: { user: userProfile }
  })
}))

// Update user profile
router.put('/profile', requireAuth, asyncHandler(async (req, res) => {
  const validatedData = updateProfileSchema.parse(req.body)
  
  const updatedProfile = await updateUserProfile(req.user.id, validatedData)

  res.json({
    success: true,
    data: { user: updatedProfile },
    message: 'Profile updated successfully'
  })
}))

// Refresh token
router.post('/refresh', asyncHandler(async (req, res) => {
  const { refreshToken } = req.body

  if (!refreshToken) {
    return res.status(400).json({
      success: false,
      error: {
        code: 'MISSING_REFRESH_TOKEN',
        message: 'Refresh token is required'
      }
    })
  }

  try {
    const decoded = verifyToken(refreshToken)
    
    if (decoded.type !== 'refresh') {
      throw new Error('Invalid token type')
    }

    const userProfile = await getUserProfile(decoded.id)
    
    if (!userProfile) {
      throw new Error('User not found')
    }

    const tokens = generateTokens(userProfile)

    res.json({
      success: true,
      data: { tokens },
      message: 'Token refreshed successfully'
    })
  } catch (error) {
    res.status(401).json({
      success: false,
      error: {
        code: 'INVALID_REFRESH_TOKEN',
        message: 'Invalid or expired refresh token'
      }
    })
  }
}))

// Logout user
router.post('/logout', requireAuth, asyncHandler(async (req, res) => {
  // In a more complex setup, you might want to blacklist the token
  // For now, we'll just return success as the client should discard the token
  
  res.json({
    success: true,
    message: 'Logout successful'
  })
}))

// Change password
router.post('/change-password', requireAuth, asyncHandler(async (req, res) => {
  const { currentPassword, newPassword } = req.body

  if (!currentPassword || !newPassword) {
    return res.status(400).json({
      success: false,
      error: {
        code: 'MISSING_PASSWORDS',
        message: 'Current password and new password are required'
      }
    })
  }

  // Update password in Supabase Auth
  const { error } = await supabaseAdmin.auth.admin.updateUserById(req.user.id, {
    password: newPassword
  })

  if (error) {
    return res.status(400).json({
      success: false,
      error: {
        code: 'PASSWORD_UPDATE_FAILED',
        message: error.message
      }
    })
  }

  res.json({
    success: true,
    message: 'Password changed successfully'
  })
}))

// Request password reset
router.post('/forgot-password', asyncHandler(async (req, res) => {
  const { email } = req.body

  if (!email) {
    return res.status(400).json({
      success: false,
      error: {
        code: 'MISSING_EMAIL',
        message: 'Email is required'
      }
    })
  }

  const { error } = await supabaseAnon.auth.resetPasswordForEmail(email, {
    redirectTo: `${process.env.FRONTEND_URL}/reset-password`
  })

  if (error) {
    return res.status(400).json({
      success: false,
      error: {
        code: 'PASSWORD_RESET_FAILED',
        message: error.message
      }
    })
  }

  res.json({
    success: true,
    message: 'Password reset email sent'
  })
}))

// Admin routes
router.get('/users', requireRole(['admin', 'support']), asyncHandler(async (req, res) => {
  const { page = 1, limit = 20, search } = req.query
  const offset = (Number(page) - 1) * Number(limit)

  let query = supabaseAdmin
    .from('users')
    .select('id, email, full_name, role, created_at, updated_at', { count: 'exact' })
    .range(offset, offset + Number(limit) - 1)
    .order('created_at', { ascending: false })

  if (search) {
    query = query.or(`email.ilike.%${search}%,full_name.ilike.%${search}%`)
  }

  const { data, error, count } = await query

  if (error) {
    throw new Error(`Failed to fetch users: ${error.message}`)
  }

  res.json({
    success: true,
    data: {
      users: data,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: count || 0,
        totalPages: Math.ceil((count || 0) / Number(limit))
      }
    }
  })
}))

router.put('/users/:userId/role', requireRole('admin'), asyncHandler(async (req, res) => {
  const { userId } = req.params
  const { role } = req.body

  if (!['user', 'admin', 'support'].includes(role)) {
    return res.status(400).json({
      success: false,
      error: {
        code: 'INVALID_ROLE',
        message: 'Invalid role specified'
      }
    })
  }

  const updatedUser = await updateUserProfile(userId, { role })

  res.json({
    success: true,
    data: { user: updatedUser },
    message: 'User role updated successfully'
  })
}))

export default router

