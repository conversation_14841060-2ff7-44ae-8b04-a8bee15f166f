import express from 'express'
import { z } from 'zod'
import { supabase } from '../../../shared/database'
import { asyncHandler } from '../middleware/asyncHandler'
import { AuthenticatedRequest } from '../middleware/authMiddleware'
import { v4 as uuidv4 } from 'uuid'
import slugify from 'slugify'

const router = express.Router()

// Validation schemas
const createPageSchema = z.object({
  site_id: z.string().uuid('Invalid site ID'),
  title: z.string().min(1, 'Page title is required'),
  slug: z.string().optional(),
  content: z.record(z.any()).optional(),
  meta_title: z.string().optional(),
  meta_description: z.string().optional(),
  settings: z.record(z.any()).optional()
})

const updatePageSchema = z.object({
  title: z.string().min(1).optional(),
  slug: z.string().optional(),
  content: z.record(z.any()).optional(),
  meta_title: z.string().optional(),
  meta_description: z.string().optional(),
  settings: z.record(z.any()).optional(),
  status: z.enum(['draft', 'published', 'archived']).optional()
})

// Get all pages for a site
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { site_id, page = 1, limit = 20, search, status } = req.query
  
  if (!site_id) {
    return res.status(400).json({
      success: false,
      error: {
        code: 'MISSING_SITE_ID',
        message: 'Site ID is required'
      }
    })
  }

  // Verify user owns the site
  const { data: site, error: siteError } = await supabase
    .from('sites')
    .select('id')
    .eq('id', site_id)
    .eq('user_id', req.user!.id)
    .single()

  if (siteError || !site) {
    return res.status(404).json({
      success: false,
      error: {
        code: 'SITE_NOT_FOUND',
        message: 'Site not found'
      }
    })
  }

  const offset = (Number(page) - 1) * Number(limit)

  let query = supabase
    .from('pages')
    .select('*', { count: 'exact' })
    .eq('site_id', site_id)
    .range(offset, offset + Number(limit) - 1)
    .order('created_at', { ascending: false })

  if (search) {
    query = query.or(`title.ilike.%${search}%,slug.ilike.%${search}%`)
  }

  if (status) {
    query = query.eq('status', status)
  }

  const { data, error, count } = await query

  if (error) {
    throw new Error(`Failed to fetch pages: ${error.message}`)
  }

  res.json({
    success: true,
    data: {
      pages: data,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: count || 0,
        totalPages: Math.ceil((count || 0) / Number(limit))
      }
    }
  })
}))

// Get single page
router.get('/:pageId', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { pageId } = req.params

  // Get page with site verification
  const { data, error } = await supabase
    .from('pages')
    .select(`
      *,
      sites!inner(user_id)
    `)
    .eq('id', pageId)
    .eq('sites.user_id', req.user!.id)
    .single()

  if (error) {
    if (error.code === 'PGRST116') {
      return res.status(404).json({
        success: false,
        error: {
          code: 'PAGE_NOT_FOUND',
          message: 'Page not found'
        }
      })
    }
    throw new Error(`Failed to fetch page: ${error.message}`)
  }

  res.json({
    success: true,
    data: { page: data }
  })
}))

// Create new page
router.post('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const validatedData = createPageSchema.parse(req.body)

  // Verify user owns the site
  const { data: site, error: siteError } = await supabase
    .from('sites')
    .select('id')
    .eq('id', validatedData.site_id)
    .eq('user_id', req.user!.id)
    .single()

  if (siteError || !site) {
    return res.status(404).json({
      success: false,
      error: {
        code: 'SITE_NOT_FOUND',
        message: 'Site not found'
      }
    })
  }

  const pageSlug = validatedData.slug || slugify(validatedData.title, { lower: true, strict: true })

  // Check if slug already exists for this site
  const { data: existingPage } = await supabase
    .from('pages')
    .select('id')
    .eq('site_id', validatedData.site_id)
    .eq('slug', pageSlug)
    .single()

  if (existingPage) {
    return res.status(409).json({
      success: false,
      error: {
        code: 'SLUG_ALREADY_EXISTS',
        message: 'A page with this slug already exists'
      }
    })
  }

  const pageData = {
    id: uuidv4(),
    site_id: validatedData.site_id,
    title: validatedData.title,
    slug: pageSlug,
    content: validatedData.content || {},
    meta_title: validatedData.meta_title,
    meta_description: validatedData.meta_description,
    settings: validatedData.settings || {},
    status: 'draft' as const,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }

  const { data, error } = await supabase
    .from('pages')
    .insert(pageData)
    .select()
    .single()

  if (error) {
    throw new Error(`Failed to create page: ${error.message}`)
  }

  res.status(201).json({
    success: true,
    data: { page: data },
    message: 'Page created successfully'
  })
}))

// Update page
router.put('/:pageId', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { pageId } = req.params
  const validatedData = updatePageSchema.parse(req.body)

  // Verify user owns the page through site ownership
  const { data: existingPage, error: fetchError } = await supabase
    .from('pages')
    .select(`
      id,
      site_id,
      sites!inner(user_id)
    `)
    .eq('id', pageId)
    .eq('sites.user_id', req.user!.id)
    .single()

  if (fetchError || !existingPage) {
    return res.status(404).json({
      success: false,
      error: {
        code: 'PAGE_NOT_FOUND',
        message: 'Page not found'
      }
    })
  }

  const updateData = {
    ...validatedData,
    updated_at: new Date().toISOString()
  }

  // If slug is being updated, check for conflicts
  if (validatedData.slug) {
    const { data: conflictingPage } = await supabase
      .from('pages')
      .select('id')
      .eq('site_id', existingPage.site_id)
      .eq('slug', validatedData.slug)
      .neq('id', pageId)
      .single()

    if (conflictingPage) {
      return res.status(409).json({
        success: false,
        error: {
          code: 'SLUG_ALREADY_EXISTS',
          message: 'A page with this slug already exists'
        }
      })
    }
  }

  const { data, error } = await supabase
    .from('pages')
    .update(updateData)
    .eq('id', pageId)
    .select()
    .single()

  if (error) {
    throw new Error(`Failed to update page: ${error.message}`)
  }

  res.json({
    success: true,
    data: { page: data },
    message: 'Page updated successfully'
  })
}))

// Delete page
router.delete('/:pageId', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { pageId } = req.params

  // Verify user owns the page through site ownership
  const { data: existingPage, error: fetchError } = await supabase
    .from('pages')
    .select(`
      id,
      sites!inner(user_id)
    `)
    .eq('id', pageId)
    .eq('sites.user_id', req.user!.id)
    .single()

  if (fetchError || !existingPage) {
    return res.status(404).json({
      success: false,
      error: {
        code: 'PAGE_NOT_FOUND',
        message: 'Page not found'
      }
    })
  }

  const { error } = await supabase
    .from('pages')
    .delete()
    .eq('id', pageId)

  if (error) {
    throw new Error(`Failed to delete page: ${error.message}`)
  }

  res.json({
    success: true,
    message: 'Page deleted successfully'
  })
}))

// Duplicate page
router.post('/:pageId/duplicate', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { pageId } = req.params
  const { title, slug } = req.body

  if (!title) {
    return res.status(400).json({
      success: false,
      error: {
        code: 'MISSING_TITLE',
        message: 'New page title is required'
      }
    })
  }

  // Get original page with site verification
  const { data: originalPage, error: fetchError } = await supabase
    .from('pages')
    .select(`
      *,
      sites!inner(user_id)
    `)
    .eq('id', pageId)
    .eq('sites.user_id', req.user!.id)
    .single()

  if (fetchError || !originalPage) {
    return res.status(404).json({
      success: false,
      error: {
        code: 'PAGE_NOT_FOUND',
        message: 'Original page not found'
      }
    })
  }

  const newSlug = slug || slugify(title, { lower: true, strict: true })

  // Check if slug already exists
  const { data: existingPage } = await supabase
    .from('pages')
    .select('id')
    .eq('site_id', originalPage.site_id)
    .eq('slug', newSlug)
    .single()

  if (existingPage) {
    return res.status(409).json({
      success: false,
      error: {
        code: 'SLUG_ALREADY_EXISTS',
        message: 'A page with this slug already exists'
      }
    })
  }

  // Create duplicate
  const duplicateData = {
    id: uuidv4(),
    site_id: originalPage.site_id,
    title,
    slug: newSlug,
    content: originalPage.content,
    meta_title: originalPage.meta_title,
    meta_description: originalPage.meta_description,
    settings: originalPage.settings,
    status: 'draft' as const,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }

  const { data, error } = await supabase
    .from('pages')
    .insert(duplicateData)
    .select()
    .single()

  if (error) {
    throw new Error(`Failed to duplicate page: ${error.message}`)
  }

  res.status(201).json({
    success: true,
    data: { page: data },
    message: 'Page duplicated successfully'
  })
}))

export default router

