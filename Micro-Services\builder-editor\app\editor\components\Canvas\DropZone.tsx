'use client'

import { Box, Text, useColorModeValue } from '@chakra-ui/react'
import { motion, AnimatePresence } from 'framer-motion'

interface DropZoneProps {
  index: number
  isActive: boolean
  onDragOver: (e: React.DragEvent) => void
  onDragLeave: () => void
  onDrop: (e: React.DragEvent) => void
}

export function DropZone({ 
  index, 
  isActive, 
  onDragOver, 
  onDragLeave, 
  onDrop 
}: DropZoneProps) {
  const borderColor = useColorModeValue('blue.400', 'blue.300')
  const bgColor = useColorModeValue('blue.50', 'blue.900')

  return (
    <Box
      position="relative"
      h={isActive ? "60px" : "20px"}
      transition="height 0.2s ease"
      onDragOver={onDragOver}
      onDragLeave={onDragLeave}
      onDrop={onDrop}
    >
      <AnimatePresence>
        {isActive && (
          <motion.div
            initial={{ opacity: 0, scaleY: 0 }}
            animate={{ opacity: 1, scaleY: 1 }}
            exit={{ opacity: 0, scaleY: 0 }}
            transition={{ duration: 0.2 }}
            style={{
              position: 'absolute',
              top: '10px',
              left: '20px',
              right: '20px',
              height: '40px',
              border: `2px dashed ${borderColor}`,
              borderRadius: '8px',
              backgroundColor: bgColor,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <Text
              fontSize="sm"
              fontWeight="medium"
              color="blue.600"
            >
              Drop section here
            </Text>
          </motion.div>
        )}
      </AnimatePresence>
    </Box>
  )
}
