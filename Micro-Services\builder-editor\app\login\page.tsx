'use client'

import { useState } from 'react'
import {
  Box,
  Container,
  VStack,
  Heading,
  Text,
  Input,
  Button,
  FormControl,
  FormLabel,
  Alert,
  AlertIcon,
  Link,
  Divider,
  useColorModeValue
} from '@chakra-ui/react'
import { createClient } from '@/utils/supabase/client'
import { useRouter } from 'next/navigation'

export default function LoginPage() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [isSignUp, setIsSignUp] = useState(false)
  
  const router = useRouter()
  const supabase = createClient()
  
  const bgColor = useColorModeValue('gray.50', 'gray.900')
  const cardBg = useColorModeValue('white', 'gray.800')

  const handleAuth = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      if (isSignUp) {
        const { error } = await supabase.auth.signUp({
          email,
          password,
        })
        if (error) throw error
        setError('Check your email for the confirmation link!')
      } else {
        const { error } = await supabase.auth.signInWithPassword({
          email,
          password,
        })
        if (error) throw error
        router.push('/editor')
      }
    } catch (error: any) {
      setError(error.message)
    } finally {
      setLoading(false)
    }
  }

  const handleDemoAccess = () => {
    // For development purposes, allow demo access
    router.push('/editor/demo')
  }

  return (
    <Box minH="100vh" bg={bgColor} py={12}>
      <Container maxW="md">
        <VStack spacing={8}>
          <VStack spacing={2} textAlign="center">
            <Heading size="xl" color="blue.500">
              Website Builder
            </Heading>
            <Text color="gray.500">
              {isSignUp ? 'Create your account' : 'Sign in to your account'}
            </Text>
          </VStack>

          <Box
            bg={cardBg}
            p={8}
            borderRadius="lg"
            boxShadow="lg"
            w="100%"
          >
            <form onSubmit={handleAuth}>
              <VStack spacing={4}>
                <FormControl>
                  <FormLabel>Email</FormLabel>
                  <Input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Enter your email"
                    required
                  />
                </FormControl>

                <FormControl>
                  <FormLabel>Password</FormLabel>
                  <Input
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Enter your password"
                    required
                  />
                </FormControl>

                {error && (
                  <Alert status="error" borderRadius="md">
                    <AlertIcon />
                    {error}
                  </Alert>
                )}

                <Button
                  type="submit"
                  colorScheme="blue"
                  size="lg"
                  w="100%"
                  isLoading={loading}
                  loadingText={isSignUp ? 'Creating account...' : 'Signing in...'}
                >
                  {isSignUp ? 'Create Account' : 'Sign In'}
                </Button>

                <Text fontSize="sm" textAlign="center">
                  {isSignUp ? 'Already have an account?' : "Don't have an account?"}{' '}
                  <Link
                    color="blue.500"
                    onClick={() => setIsSignUp(!isSignUp)}
                    cursor="pointer"
                  >
                    {isSignUp ? 'Sign in' : 'Sign up'}
                  </Link>
                </Text>
              </VStack>
            </form>

            <Divider my={6} />

            <VStack spacing={3}>
              <Text fontSize="sm" color="gray.500" textAlign="center">
                Development Mode
              </Text>
              <Button
                variant="outline"
                colorScheme="gray"
                size="sm"
                onClick={handleDemoAccess}
                w="100%"
              >
                🚀 Try Demo Editor (No Login Required)
              </Button>
            </VStack>
          </Box>

          <Text fontSize="xs" color="gray.400" textAlign="center">
            By signing in, you agree to our Terms of Service and Privacy Policy
          </Text>
        </VStack>
      </Container>
    </Box>
  )
}
