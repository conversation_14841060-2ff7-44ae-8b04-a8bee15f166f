'use client'

import React, { useState } from 'react'
import {
  Box,
  VStack,
  HStack,
  Text,
  Input,
  Button,
  FormControl,
  FormLabel,
  Slider,
  SliderTrack,
  SliderFilledTrack,
  SliderThumb,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  IconButton,
  useColorModeValue,
  SimpleGrid,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Select,
  Switch
} from '@chakra-ui/react'
import { EditIcon, SearchIcon } from '@chakra-ui/icons'

// Popular icon collections
const iconCollections = {
  general: [
    'home', 'user', 'users', 'heart', 'star', 'search', 'settings', 'bell', 'mail', 'phone',
    'location', 'calendar', 'clock', 'download', 'upload', 'share', 'link', 'edit', 'delete', 'add'
  ],
  business: [
    'briefcase', 'chart', 'graph', 'money', 'credit-card', 'bank', 'building', 'office',
    'handshake', 'target', 'trophy', 'award', 'certificate', 'presentation', 'meeting', 'team'
  ],
  social: [
    'facebook', 'twitter', 'instagram', 'linkedin', 'youtube', 'pinterest', 'snapchat', 'tiktok',
    'whatsapp', 'telegram', 'discord', 'reddit', 'github', 'dribbble', 'behance', 'medium'
  ],
  ecommerce: [
    'shopping-cart', 'shopping-bag', 'store', 'tag', 'gift', 'package', 'truck', 'delivery',
    'payment', 'receipt', 'discount', 'sale', 'wishlist', 'compare', 'review', 'rating'
  ],
  arrows: [
    'arrow-up', 'arrow-down', 'arrow-left', 'arrow-right', 'arrow-up-right', 'arrow-down-left',
    'chevron-up', 'chevron-down', 'chevron-left', 'chevron-right', 'expand', 'collapse'
  ]
}

export interface IconSettings {
  name: string
  collection: keyof typeof iconCollections
  size: number
  color: string
  style: {
    backgroundColor: string
    borderRadius: number
    padding: number
    border: string
    shadow: string
  }
  animation: {
    enabled: boolean
    type: 'none' | 'spin' | 'pulse' | 'bounce' | 'shake'
    duration: number
  }
  link: {
    enabled: boolean
    url: string
    target: '_self' | '_blank'
  }
}

interface IconProps {
  settings: IconSettings
  onChange?: (settings: IconSettings) => void
  isEditing?: boolean
  className?: string
  style?: React.CSSProperties
}

const defaultSettings: IconSettings = {
  name: 'home',
  collection: 'general',
  size: 24,
  color: '#333333',
  style: {
    backgroundColor: 'transparent',
    borderRadius: 0,
    padding: 8,
    border: 'none',
    shadow: 'none'
  },
  animation: {
    enabled: false,
    type: 'none',
    duration: 1
  },
  link: {
    enabled: false,
    url: '',
    target: '_self'
  }
}

// Icon Component using CSS classes (similar to Old Builder's approach)
const IconElement: React.FC<{ name: string, size: number, color: string, className?: string }> = ({
  name,
  size,
  color,
  className
}) => {
  // Using Font Awesome classes as fallback, similar to Old Builder
  return (
    <i
      className={`fa fa-${name} ${className || ''}`}
      style={{
        fontSize: `${size}px`,
        color: color,
        lineHeight: 1
      }}
    />
  )
}

const Icon: React.FC<IconProps> = ({
  settings = defaultSettings,
  onChange,
  isEditing = false,
  className,
  style
}) => {
  const { isOpen, onOpen, onClose } = useDisclosure()

  const getAnimationStyle = () => {
    if (!settings.animation.enabled || settings.animation.type === 'none') return {}

    const animations = {
      spin: `spin ${settings.animation.duration}s linear infinite`,
      pulse: `pulse ${settings.animation.duration}s ease-in-out infinite`,
      bounce: `bounce ${settings.animation.duration}s ease-in-out infinite`,
      shake: `shake ${settings.animation.duration}s ease-in-out infinite`
    }

    return {
      animation: animations[settings.animation.type] || 'none'
    }
  }

  const iconStyle = {
    backgroundColor: settings.style.backgroundColor,
    borderRadius: `${settings.style.borderRadius}px`,
    padding: `${settings.style.padding}px`,
    border: settings.style.border,
    boxShadow: settings.style.shadow,
    display: 'inline-flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: settings.link.enabled ? 'pointer' : 'default',
    ...getAnimationStyle()
  }

  const handleClick = () => {
    if (settings.link.enabled && settings.link.url) {
      if (settings.link.target === '_blank') {
        window.open(settings.link.url, '_blank')
      } else {
        window.location.href = settings.link.url
      }
    }
  }

  if (isEditing) {
    return (
      <Box className={className} style={style}>
        <Button onClick={onOpen} size="sm" colorScheme="blue">
          Edit Icon
        </Button>
        
        <Modal isOpen={isOpen} onClose={onClose} size="xl">
          <ModalOverlay />
          <ModalContent>
            <ModalHeader>Icon Settings</ModalHeader>
            <ModalCloseButton />
            <ModalBody pb={6}>
              <IconSettings
                settings={settings}
                onChange={onChange || (() => {})}
              />
            </ModalBody>
          </ModalContent>
        </Modal>

        {/* Preview */}
        <Box mt={4} p={4} border="1px dashed" borderColor="gray.300" borderRadius="md">
          <Text fontSize="sm" color="gray.500" mb={2}>Preview:</Text>
          <Box style={iconStyle} onClick={handleClick}>
            <IconElement
              name={settings.name}
              size={settings.size}
              color={settings.color}
            />
          </Box>
        </Box>
      </Box>
    )
  }

  return (
    <Box className={className} style={style}>
      <Box style={iconStyle} onClick={handleClick}>
        <IconElement
          name={settings.name}
          size={settings.size}
          color={settings.color}
        />
      </Box>
      
      {isEditing && (
        <IconButton
          aria-label="Edit icon"
          icon={<EditIcon />}
          size="sm"
          position="absolute"
          top={2}
          right={2}
          zIndex={2}
          onClick={onOpen}
          colorScheme="blue"
        />
      )}
    </Box>
  )
}

// Settings Component
const IconSettings: React.FC<{
  settings: IconSettings
  onChange: (settings: IconSettings) => void
}> = ({ settings, onChange }) => {
  const [searchTerm, setSearchTerm] = useState('')
  const borderColor = useColorModeValue('gray.200', 'gray.600')

  const filteredIcons = iconCollections[settings.collection].filter(icon =>
    icon.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <VStack spacing={6} align="stretch">
      <Tabs>
        <TabList>
          <Tab>Icon</Tab>
          <Tab>Style</Tab>
          <Tab>Animation</Tab>
          <Tab>Link</Tab>
        </TabList>

        <TabPanels>
          {/* Icon Selection Tab */}
          <TabPanel>
            <VStack spacing={4} align="stretch">
              {/* Collection Selector */}
              <FormControl>
                <FormLabel fontSize="sm">Icon Collection</FormLabel>
                <Select
                  value={settings.collection}
                  onChange={(e) => onChange({
                    ...settings,
                    collection: e.target.value as keyof typeof iconCollections,
                    name: iconCollections[e.target.value as keyof typeof iconCollections][0]
                  })}
                  size="sm"
                >
                  {Object.keys(iconCollections).map((collection) => (
                    <option key={collection} value={collection}>
                      {collection.charAt(0).toUpperCase() + collection.slice(1)}
                    </option>
                  ))}
                </Select>
              </FormControl>

              {/* Search */}
              <FormControl>
                <FormLabel fontSize="sm">Search Icons</FormLabel>
                <Input
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Search for icons..."
                  size="sm"
                  leftElement={<SearchIcon />}
                />
              </FormControl>

              {/* Icon Grid */}
              <Box>
                <Text fontSize="sm" fontWeight="semibold" mb={3}>
                  Select Icon ({filteredIcons.length} available)
                </Text>
                <SimpleGrid columns={6} spacing={2} maxH="300px" overflowY="auto">
                  {filteredIcons.map((iconName) => (
                    <Button
                      key={iconName}
                      variant={settings.name === iconName ? 'solid' : 'outline'}
                      colorScheme="blue"
                      size="sm"
                      p={3}
                      h="auto"
                      onClick={() => onChange({ ...settings, name: iconName })}
                      title={iconName}
                    >
                      <VStack spacing={1}>
                        <IconElement name={iconName} size={16} color="currentColor" />
                        <Text fontSize="xs" noOfLines={1}>
                          {iconName}
                        </Text>
                      </VStack>
                    </Button>
                  ))}
                </SimpleGrid>
              </Box>

              {/* Size and Color */}
              <HStack spacing={4}>
                <FormControl>
                  <FormLabel fontSize="sm">Size: {settings.size}px</FormLabel>
                  <Slider
                    value={settings.size}
                    onChange={(value) => onChange({ ...settings, size: value })}
                    min={12}
                    max={100}
                    step={2}
                  >
                    <SliderTrack>
                      <SliderFilledTrack />
                    </SliderTrack>
                    <SliderThumb />
                  </Slider>
                </FormControl>

                <FormControl>
                  <FormLabel fontSize="sm">Color</FormLabel>
                  <Input
                    type="color"
                    value={settings.color}
                    onChange={(e) => onChange({ ...settings, color: e.target.value })}
                    size="sm"
                    w="60px"
                  />
                </FormControl>
              </HStack>
            </VStack>
          </TabPanel>

          {/* Style Tab */}
          <TabPanel>
            <VStack spacing={4} align="stretch">
              <FormControl>
                <FormLabel fontSize="sm">Background Color</FormLabel>
                <HStack>
                  <Input
                    type="color"
                    value={settings.style.backgroundColor === 'transparent' ? '#ffffff' : settings.style.backgroundColor}
                    onChange={(e) => onChange({
                      ...settings,
                      style: { ...settings.style, backgroundColor: e.target.value }
                    })}
                    size="sm"
                    w="60px"
                  />
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => onChange({
                      ...settings,
                      style: { ...settings.style, backgroundColor: 'transparent' }
                    })}
                  >
                    Transparent
                  </Button>
                </HStack>
              </FormControl>

              <FormControl>
                <FormLabel fontSize="sm">Border Radius: {settings.style.borderRadius}px</FormLabel>
                <Slider
                  value={settings.style.borderRadius}
                  onChange={(value) => onChange({
                    ...settings,
                    style: { ...settings.style, borderRadius: value }
                  })}
                  min={0}
                  max={50}
                  step={1}
                >
                  <SliderTrack>
                    <SliderFilledTrack />
                  </SliderTrack>
                  <SliderThumb />
                </Slider>
              </FormControl>

              <FormControl>
                <FormLabel fontSize="sm">Padding: {settings.style.padding}px</FormLabel>
                <Slider
                  value={settings.style.padding}
                  onChange={(value) => onChange({
                    ...settings,
                    style: { ...settings.style, padding: value }
                  })}
                  min={0}
                  max={50}
                  step={2}
                >
                  <SliderTrack>
                    <SliderFilledTrack />
                  </SliderTrack>
                  <SliderThumb />
                </Slider>
              </FormControl>

              <FormControl>
                <FormLabel fontSize="sm">Border</FormLabel>
                <Input
                  value={settings.style.border}
                  onChange={(e) => onChange({
                    ...settings,
                    style: { ...settings.style, border: e.target.value }
                  })}
                  size="sm"
                  placeholder="1px solid #ccc"
                />
              </FormControl>

              <FormControl>
                <FormLabel fontSize="sm">Shadow</FormLabel>
                <Select
                  value={settings.style.shadow}
                  onChange={(e) => onChange({
                    ...settings,
                    style: { ...settings.style, shadow: e.target.value }
                  })}
                  size="sm"
                >
                  <option value="none">None</option>
                  <option value="0 1px 3px rgba(0,0,0,0.1)">Small</option>
                  <option value="0 4px 6px rgba(0,0,0,0.1)">Medium</option>
                  <option value="0 10px 15px rgba(0,0,0,0.1)">Large</option>
                </Select>
              </FormControl>
            </VStack>
          </TabPanel>

          {/* Animation Tab */}
          <TabPanel>
            <VStack spacing={4} align="stretch">
              <FormControl display="flex" alignItems="center">
                <FormLabel fontSize="sm" mb="0" flex="1">
                  Enable Animation
                </FormLabel>
                <Switch
                  isChecked={settings.animation.enabled}
                  onChange={(e) => onChange({
                    ...settings,
                    animation: { ...settings.animation, enabled: e.target.checked }
                  })}
                />
              </FormControl>

              {settings.animation.enabled && (
                <>
                  <FormControl>
                    <FormLabel fontSize="sm">Animation Type</FormLabel>
                    <Select
                      value={settings.animation.type}
                      onChange={(e) => onChange({
                        ...settings,
                        animation: { ...settings.animation, type: e.target.value as any }
                      })}
                      size="sm"
                    >
                      <option value="none">None</option>
                      <option value="spin">Spin</option>
                      <option value="pulse">Pulse</option>
                      <option value="bounce">Bounce</option>
                      <option value="shake">Shake</option>
                    </Select>
                  </FormControl>

                  <FormControl>
                    <FormLabel fontSize="sm">Duration: {settings.animation.duration}s</FormLabel>
                    <Slider
                      value={settings.animation.duration}
                      onChange={(value) => onChange({
                        ...settings,
                        animation: { ...settings.animation, duration: value }
                      })}
                      min={0.5}
                      max={5}
                      step={0.1}
                    >
                      <SliderTrack>
                        <SliderFilledTrack />
                      </SliderTrack>
                      <SliderThumb />
                    </Slider>
                  </FormControl>
                </>
              )}
            </VStack>
          </TabPanel>

          {/* Link Tab */}
          <TabPanel>
            <VStack spacing={4} align="stretch">
              <FormControl display="flex" alignItems="center">
                <FormLabel fontSize="sm" mb="0" flex="1">
                  Enable Link
                </FormLabel>
                <Switch
                  isChecked={settings.link.enabled}
                  onChange={(e) => onChange({
                    ...settings,
                    link: { ...settings.link, enabled: e.target.checked }
                  })}
                />
              </FormControl>

              {settings.link.enabled && (
                <>
                  <FormControl>
                    <FormLabel fontSize="sm">URL</FormLabel>
                    <Input
                      value={settings.link.url}
                      onChange={(e) => onChange({
                        ...settings,
                        link: { ...settings.link, url: e.target.value }
                      })}
                      size="sm"
                      placeholder="https://example.com"
                    />
                  </FormControl>

                  <FormControl>
                    <FormLabel fontSize="sm">Target</FormLabel>
                    <Select
                      value={settings.link.target}
                      onChange={(e) => onChange({
                        ...settings,
                        link: { ...settings.link, target: e.target.value as '_self' | '_blank' }
                      })}
                      size="sm"
                    >
                      <option value="_self">Same Tab</option>
                      <option value="_blank">New Tab</option>
                    </Select>
                  </FormControl>
                </>
              )}
            </VStack>
          </TabPanel>
        </TabPanels>
      </Tabs>
    </VStack>
  )
}

export default Icon
