'use client'

import React, { useState } from 'react'
import {
  Box,
  VStack,
  HStack,
  Text,
  Input,
  Select,
  Button,
  FormControl,
  FormLabel,
  Switch,
  IconButton,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  Flex,
  Spacer,
  useColorModeValue,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  Collapse,
  Link
} from '@chakra-ui/react'
import { AddIcon, DeleteIcon, EditIcon, HamburgerIcon, ChevronDownIcon } from '@chakra-ui/icons'

export interface MenuItem {
  id: string
  label: string
  url: string
  target: '_self' | '_blank'
  children?: MenuItem[]
}

export interface NavMenuSettings {
  items: MenuItem[]
  layout: 'horizontal' | 'vertical'
  alignment: 'left' | 'center' | 'right'
  style: {
    backgroundColor: string
    textColor: string
    hoverColor: string
    fontSize: number
    fontWeight: string
    padding: number
    borderRadius: number
  }
  mobile: {
    enabled: boolean
    breakpoint: number
    hamburgerColor: string
  }
}

interface NavMenuProps {
  settings: NavMenuSettings
  onChange?: (settings: NavMenuSettings) => void
  isEditing?: boolean
  className?: string
  style?: React.CSSProperties
}

const defaultSettings: NavMenuSettings = {
  items: [
    { id: '1', label: 'Home', url: '/', target: '_self' },
    { id: '2', label: 'About', url: '/about', target: '_self' },
    { id: '3', label: 'Services', url: '/services', target: '_self' },
    { id: '4', label: 'Contact', url: '/contact', target: '_self' }
  ],
  layout: 'horizontal',
  alignment: 'left',
  style: {
    backgroundColor: 'transparent',
    textColor: '#333333',
    hoverColor: '#0066cc',
    fontSize: 16,
    fontWeight: 'normal',
    padding: 12,
    borderRadius: 0
  },
  mobile: {
    enabled: true,
    breakpoint: 768,
    hamburgerColor: '#333333'
  }
}

// Mobile Menu Component
const MobileMenu: React.FC<{ items: MenuItem[], style: any, isOpen: boolean, onToggle: () => void }> = ({
  items,
  style,
  isOpen,
  onToggle
}) => {
  return (
    <Box display={{ base: 'block', md: 'none' }}>
      <IconButton
        aria-label="Toggle menu"
        icon={<HamburgerIcon />}
        variant="ghost"
        color={style.textColor}
        onClick={onToggle}
      />
      <Collapse in={isOpen}>
        <VStack
          spacing={2}
          align="stretch"
          mt={4}
          p={4}
          bg={style.backgroundColor}
          borderRadius={style.borderRadius}
          boxShadow="md"
        >
          {items.map((item) => (
            <Link
              key={item.id}
              href={item.url}
              target={item.target}
              color={style.textColor}
              fontSize={style.fontSize}
              fontWeight={style.fontWeight}
              p={2}
              _hover={{ color: style.hoverColor }}
            >
              {item.label}
            </Link>
          ))}
        </VStack>
      </Collapse>
    </Box>
  )
}

// Desktop Menu Component
const DesktopMenu: React.FC<{ items: MenuItem[], settings: NavMenuSettings }> = ({ items, settings }) => {
  const { style, layout, alignment } = settings

  return (
    <Box display={{ base: 'none', md: 'block' }}>
      <Flex
        direction={layout === 'horizontal' ? 'row' : 'column'}
        align="center"
        justify={alignment}
        gap={4}
        bg={style.backgroundColor}
        p={style.padding}
        borderRadius={style.borderRadius}
      >
        {items.map((item) => (
          <Box key={item.id} position="relative">
            {item.children && item.children.length > 0 ? (
              <Menu>
                <MenuButton
                  as={Button}
                  variant="ghost"
                  color={style.textColor}
                  fontSize={style.fontSize}
                  fontWeight={style.fontWeight}
                  _hover={{ color: style.hoverColor }}
                  rightIcon={<ChevronDownIcon />}
                >
                  {item.label}
                </MenuButton>
                <MenuList>
                  {item.children.map((child) => (
                    <MenuItem
                      key={child.id}
                      as={Link}
                      href={child.url}
                      target={child.target}
                    >
                      {child.label}
                    </MenuItem>
                  ))}
                </MenuList>
              </Menu>
            ) : (
              <Link
                href={item.url}
                target={item.target}
                color={style.textColor}
                fontSize={style.fontSize}
                fontWeight={style.fontWeight}
                p={style.padding}
                _hover={{ color: style.hoverColor }}
                textDecoration="none"
              >
                {item.label}
              </Link>
            )}
          </Box>
        ))}
      </Flex>
    </Box>
  )
}

const NavMenu: React.FC<NavMenuProps> = ({
  settings = defaultSettings,
  onChange,
  isEditing = false,
  className,
  style
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const { isOpen: isModalOpen, onOpen, onClose } = useDisclosure()

  const handleToggle = () => setIsOpen(!isOpen)

  if (isEditing) {
    return (
      <Box className={className} style={style}>
        <Button onClick={onOpen} size="sm" colorScheme="blue">
          Edit Navigation Menu
        </Button>
        
        <Modal isOpen={isModalOpen} onClose={onClose} size="xl">
          <ModalOverlay />
          <ModalContent>
            <ModalHeader>Navigation Menu Settings</ModalHeader>
            <ModalCloseButton />
            <ModalBody pb={6}>
              <NavMenuSettings
                settings={settings}
                onChange={onChange || (() => {})}
              />
            </ModalBody>
          </ModalContent>
        </Modal>

        {/* Preview */}
        <Box mt={4} p={4} border="1px dashed" borderColor="gray.300" borderRadius="md">
          <Text fontSize="sm" color="gray.500" mb={2}>Preview:</Text>
          <DesktopMenu items={settings.items} settings={settings} />
          <MobileMenu
            items={settings.items}
            style={settings.style}
            isOpen={isOpen}
            onToggle={handleToggle}
          />
        </Box>
      </Box>
    )
  }

  return (
    <Box className={className} style={style}>
      <DesktopMenu items={settings.items} settings={settings} />
      <MobileMenu
        items={settings.items}
        style={settings.style}
        isOpen={isOpen}
        onToggle={handleToggle}
      />
    </Box>
  )
}

// Settings Component
const NavMenuSettings: React.FC<{
  settings: NavMenuSettings
  onChange: (settings: NavMenuSettings) => void
}> = ({ settings, onChange }) => {
  const borderColor = useColorModeValue('gray.200', 'gray.600')

  const addMenuItem = () => {
    const newItem: MenuItem = {
      id: Date.now().toString(),
      label: 'New Item',
      url: '#',
      target: '_self'
    }
    onChange({
      ...settings,
      items: [...settings.items, newItem]
    })
  }

  const updateMenuItem = (id: string, updates: Partial<MenuItem>) => {
    onChange({
      ...settings,
      items: settings.items.map(item =>
        item.id === id ? { ...item, ...updates } : item
      )
    })
  }

  const deleteMenuItem = (id: string) => {
    onChange({
      ...settings,
      items: settings.items.filter(item => item.id !== id)
    })
  }

  return (
    <VStack spacing={6} align="stretch">
      {/* Menu Items */}
      <Box>
        <HStack justify="space-between" mb={4}>
          <Text fontWeight="semibold">Menu Items</Text>
          <Button size="sm" leftIcon={<AddIcon />} onClick={addMenuItem}>
            Add Item
          </Button>
        </HStack>
        
        <VStack spacing={3} align="stretch">
          {settings.items.map((item) => (
            <Box key={item.id} p={3} border="1px" borderColor={borderColor} borderRadius="md">
              <HStack spacing={3}>
                <FormControl flex="1">
                  <Input
                    value={item.label}
                    onChange={(e) => updateMenuItem(item.id, { label: e.target.value })}
                    placeholder="Label"
                    size="sm"
                  />
                </FormControl>
                <FormControl flex="1">
                  <Input
                    value={item.url}
                    onChange={(e) => updateMenuItem(item.id, { url: e.target.value })}
                    placeholder="URL"
                    size="sm"
                  />
                </FormControl>
                <Select
                  value={item.target}
                  onChange={(e) => updateMenuItem(item.id, { target: e.target.value as '_self' | '_blank' })}
                  size="sm"
                  w="120px"
                >
                  <option value="_self">Same Tab</option>
                  <option value="_blank">New Tab</option>
                </Select>
                <IconButton
                  aria-label="Delete item"
                  icon={<DeleteIcon />}
                  size="sm"
                  colorScheme="red"
                  variant="ghost"
                  onClick={() => deleteMenuItem(item.id)}
                />
              </HStack>
            </Box>
          ))}
        </VStack>
      </Box>

      {/* Layout Settings */}
      <Box>
        <Text fontWeight="semibold" mb={3}>Layout</Text>
        <VStack spacing={3} align="stretch">
          <FormControl>
            <FormLabel fontSize="sm">Layout Direction</FormLabel>
            <Select
              value={settings.layout}
              onChange={(e) => onChange({
                ...settings,
                layout: e.target.value as 'horizontal' | 'vertical'
              })}
              size="sm"
            >
              <option value="horizontal">Horizontal</option>
              <option value="vertical">Vertical</option>
            </Select>
          </FormControl>

          <FormControl>
            <FormLabel fontSize="sm">Alignment</FormLabel>
            <Select
              value={settings.alignment}
              onChange={(e) => onChange({
                ...settings,
                alignment: e.target.value as 'left' | 'center' | 'right'
              })}
              size="sm"
            >
              <option value="left">Left</option>
              <option value="center">Center</option>
              <option value="right">Right</option>
            </Select>
          </FormControl>
        </VStack>
      </Box>
    </VStack>
  )
}

export default NavMenu
