'use client'

import React from 'react'
import {
  Box,
  HStack,
  IconButton,
  Tooltip,
  useColorModeValue,
  Portal
} from '@chakra-ui/react'
import {
  DragHandleIcon,
  ViewIcon,
  ViewOffIcon,
  CopyIcon,
  DeleteIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  EditIcon
} from '@chakra-ui/icons'
import { useEditorStore } from '@/lib/stores/editorStore'

interface ElementControlsProps {
  elementId: string
  sectionId: string
  isVisible?: boolean
  canMoveUp?: boolean
  canMoveDown?: boolean
  position?: { x: number; y: number }
  onEdit?: () => void
}

export function ElementControls({
  elementId,
  sectionId,
  isVisible = true,
  canMoveUp = true,
  canMoveDown = true,
  position,
  onEdit
}: ElementControlsProps) {
  const {
    duplicateElement,
    deleteElement,
    moveElementUp,
    moveElementDown,
    toggleElementVisibility,
    setSelectedElement
  } = useEditorStore()

  const bgColor = useColorModeValue('white', 'gray.800')
  const borderColor = useColorModeValue('gray.200', 'gray.600')

  const handleDuplicate = () => {
    duplicateElement(sectionId, elementId)
  }

  const handleDelete = () => {
    deleteElement(sectionId, elementId)
  }

  const handleMoveUp = () => {
    moveElementUp(sectionId, elementId)
  }

  const handleMoveDown = () => {
    moveElementDown(sectionId, elementId)
  }

  const handleToggleVisibility = () => {
    toggleElementVisibility(sectionId, elementId)
  }

  const handleEdit = () => {
    setSelectedElement(elementId)
    onEdit?.()
  }

  const controlsStyle: React.CSSProperties = {
    position: 'absolute',
    top: position?.y || 0,
    left: position?.x || 0,
    zIndex: 1000,
    transform: 'translate(-50%, -100%)',
    marginTop: '-8px'
  }

  return (
    <Portal>
      <Box style={controlsStyle}>
        <HStack
          spacing={1}
          bg={bgColor}
          border="1px"
          borderColor={borderColor}
          borderRadius="md"
          p={1}
          boxShadow="lg"
        >
          {/* Drag Handle */}
          <Tooltip label="Drag to move">
            <IconButton
              aria-label="Drag element"
              icon={<DragHandleIcon />}
              size="xs"
              variant="ghost"
              cursor="grab"
              _active={{ cursor: 'grabbing' }}
            />
          </Tooltip>

          {/* Edit */}
          <Tooltip label="Edit element">
            <IconButton
              aria-label="Edit element"
              icon={<EditIcon />}
              size="xs"
              variant="ghost"
              onClick={handleEdit}
            />
          </Tooltip>

          {/* Move Up */}
          <Tooltip label="Move up">
            <IconButton
              aria-label="Move up"
              icon={<ArrowUpIcon />}
              size="xs"
              variant="ghost"
              isDisabled={!canMoveUp}
              onClick={handleMoveUp}
            />
          </Tooltip>

          {/* Move Down */}
          <Tooltip label="Move down">
            <IconButton
              aria-label="Move down"
              icon={<ArrowDownIcon />}
              size="xs"
              variant="ghost"
              isDisabled={!canMoveDown}
              onClick={handleMoveDown}
            />
          </Tooltip>

          {/* Toggle Visibility */}
          <Tooltip label={isVisible ? 'Hide element' : 'Show element'}>
            <IconButton
              aria-label={isVisible ? 'Hide element' : 'Show element'}
              icon={isVisible ? <ViewIcon /> : <ViewOffIcon />}
              size="xs"
              variant="ghost"
              colorScheme={isVisible ? 'gray' : 'orange'}
              onClick={handleToggleVisibility}
            />
          </Tooltip>

          {/* Duplicate */}
          <Tooltip label="Duplicate element">
            <IconButton
              aria-label="Duplicate element"
              icon={<CopyIcon />}
              size="xs"
              variant="ghost"
              onClick={handleDuplicate}
            />
          </Tooltip>

          {/* Delete */}
          <Tooltip label="Delete element">
            <IconButton
              aria-label="Delete element"
              icon={<DeleteIcon />}
              size="xs"
              variant="ghost"
              colorScheme="red"
              onClick={handleDelete}
            />
          </Tooltip>
        </HStack>
      </Box>
    </Portal>
  )
}

export default ElementControls
