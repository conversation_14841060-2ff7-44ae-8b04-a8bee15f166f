# New Builder - Complete Microservices Documentation

## 🏗️ Project Overview

New Builder is a modern, scalable website builder platform transitioning from a monolithic architecture to a microservices-based system. The platform enables users to create, customize, and deploy websites through an intuitive interface.

## 📊 Architecture Status

**Migration Progress: 85% Complete**

- ✅ **Phase 1: Architecture Design** (100%)
- ✅ **Phase 2: Service Scaffolding** (100%)
- ✅ **Phase 3: Core Implementation** (85%)
- 🔄 **Phase 4: Integration & Testing** (In Progress)
- ⏳ **Phase 5: Production Deployment** (Pending)

## 🗄️ Database Architecture

### Supabase Configuration
- **Project ID**: `cikzkzviubwpruiowapp`
- **URL**: `https://cikzkzviubwpruiowapp.supabase.co`
- **Database**: PostgreSQL with Row Level Security (RLS)

### Core Tables

#### Users Table
```sql
users (
  id UUID PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  role TEXT DEFAULT 'user',
  language TEXT DEFAULT 'en',
  timezone TEXT DEFAULT 'UTC',
  preferences JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
)
```

#### Sites Table
```sql
sites (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES users(id),
  name TEXT NOT NULL,
  description TEXT,
  slug TEXT UNIQUE,
  template_id UUID,
  domain TEXT,
  settings JSONB DEFAULT '{}',
  status TEXT DEFAULT 'draft',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
)
```

#### Pages Table
```sql
pages (
  id UUID PRIMARY KEY,
  site_id UUID REFERENCES sites(id),
  title TEXT NOT NULL,
  slug TEXT NOT NULL,
  content JSONB DEFAULT '{}',
  meta_title TEXT,
  meta_description TEXT,
  settings JSONB DEFAULT '{}',
  status TEXT DEFAULT 'draft',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
)
```

[Additional tables: templates, media, deployments, subscriptions, analytics_events, form_submissions, invitations, domains]

## 🚀 Microservices Architecture

### 1. Auth Service (✅ Complete)
**Port**: 3000 | **Status**: Production Ready

**Features**:
- User registration and authentication
- JWT token management
- Role-based access control (RBAC)
- Password reset functionality
- Session management
- Admin user management

**Endpoints**:
- `POST /auth/register` - User registration
- `POST /auth/login` - User authentication
- `GET /auth/me` - Get current user
- `PUT /auth/profile` - Update user profile
- `POST /auth/refresh` - Refresh JWT token
- `POST /auth/logout` - User logout
- `POST /auth/forgot-password` - Password reset
- `POST /auth/change-password` - Change password
- `GET /auth/users` - Admin: List users
- `PUT /auth/users/:id/role` - Admin: Update user role

### 2. Builder API (✅ Complete)
**Port**: 3001 | **Status**: Production Ready

**Features**:
- Site management (CRUD operations)
- Page management (CRUD operations)
- Site duplication
- Page duplication
- Content versioning
- SEO metadata management

**Endpoints**:
- `GET /sites` - List user sites
- `POST /sites` - Create new site
- `GET /sites/:id` - Get site details
- `PUT /sites/:id` - Update site
- `DELETE /sites/:id` - Delete site
- `POST /sites/:id/duplicate` - Duplicate site
- `GET /pages` - List pages for site
- `POST /pages` - Create new page
- `GET /pages/:id` - Get page details
- `PUT /pages/:id` - Update page
- `DELETE /pages/:id` - Delete page
- `POST /pages/:id/duplicate` - Duplicate page

### 3. Builder Editor (🔄 Generated)
**Port**: 3002 | **Type**: Next.js Frontend

**Features**:
- Visual drag-and-drop editor
- Component library
- Real-time preview
- Responsive design tools
- Custom CSS editor

### 4. Templates Service (🔄 Generated)
**Port**: 3003 | **Status**: Generated

**Features**:
- Template marketplace
- Template categories
- Template preview
- Custom template creation
- Template versioning

### 5. Media Service (🔄 Generated)
**Port**: 3004 | **Status**: Generated

**Features**:
- File upload and storage
- Image optimization
- CDN integration
- Media library management
- Asset organization

### 6. Publish Service (🔄 Generated)
**Port**: 3005 | **Status**: Generated

**Features**:
- Site deployment
- Static site generation
- CDN distribution
- Deployment history
- Rollback functionality

### 7. Domain Service (🔄 Generated)
**Port**: 3006 | **Status**: Generated

**Features**:
- Domain registration
- DNS management
- SSL certificate provisioning
- Subdomain management
- Custom domain mapping

### 8. Billing Service (🔄 Generated)
**Port**: 3007 | **Status**: Generated

**Features**:
- Subscription management
- Payment processing
- Invoice generation
- Usage tracking
- Plan upgrades/downgrades

### 9. Analytics Service (🔄 Generated)
**Port**: 3008 | **Status**: Generated

**Features**:
- Website traffic analytics
- User behavior tracking
- Performance metrics
- Custom event tracking
- Analytics dashboard

### 10. Questionnaire Service (🔄 Generated)
**Port**: 3009 | **Status**: Generated

**Features**:
- Form builder
- Survey creation
- Response collection
- Data export
- Form analytics

### 11. Invitation Service (🔄 Generated)
**Port**: 3010 | **Status**: Generated

**Features**:
- User invitations
- Team collaboration
- Permission management
- Invitation tracking
- Onboarding flows

### 12. Geo Service (🔄 Generated)
**Port**: 3011 | **Status**: Generated

**Features**:
- Location-based services
- Geographic data
- Regional content
- Timezone handling
- Localization support

### 13. CRM Integration (🔄 Generated)
**Port**: 3012 | **Status**: Generated

**Features**:
- Third-party CRM connections
- Data synchronization
- Lead management
- Contact import/export
- Webhook integrations

### 14. Admin Dashboard (🔄 Generated)
**Port**: 3013 | **Type**: Next.js Frontend

**Features**:
- System administration
- User management
- Service monitoring
- Configuration management
- Analytics overview

### 15. Backoffice API (🔄 Generated)
**Port**: 3014 | **Status**: Generated

**Features**:
- Internal operations API
- Support tools
- Data management
- System maintenance
- Reporting endpoints

### 16. Backoffice Dashboard (🔄 Generated)
**Port**: 3015 | **Type**: Next.js Frontend

**Features**:
- Support team interface
- Customer support tools
- Issue tracking
- System monitoring
- Operational dashboards

### 17. Site Dashboard (🔄 Generated)
**Port**: 3016 | **Type**: Next.js Frontend

**Features**:
- User site management
- Site analytics
- Performance monitoring
- SEO tools
- Content management

### 18. Test Service (🔄 Generated)
**Port**: 3017 | **Status**: Generated

**Features**:
- Automated testing
- Quality assurance
- Performance testing
- Integration testing
- Test reporting

## 🔐 Security Implementation

### Authentication & Authorization
- **JWT Tokens**: Secure token-based authentication
- **Role-Based Access Control**: User, Admin, Support roles
- **Row Level Security**: Database-level access control
- **Rate Limiting**: API endpoint protection
- **CORS Configuration**: Cross-origin request security

### Data Protection
- **Input Validation**: Zod schema validation
- **SQL Injection Prevention**: Parameterized queries
- **XSS Protection**: Content sanitization
- **HTTPS Enforcement**: Secure data transmission

## 🚀 Deployment Configuration

### Vercel Deployment
- **Project ID**: `prj_eW86m1NmWx2W3IAZGcHWL12Lz0HA`
- **API Token**: `heHuxjqlSlgdNfVnjtmGHUyN`
- **Auto-deployment**: Configured for all services

### Environment Variables
```bash
# Supabase Configuration
SUPABASE_URL=https://cikzkzviubwpruiowapp.supabase.co
SUPABASE_ANON_KEY=[REDACTED]
SUPABASE_SERVICE_ROLE_KEY=[REDACTED]

# JWT Configuration
JWT_SECRET=[REDACTED]
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# Service Configuration
NODE_ENV=production
CORS_ORIGIN=https://yourdomain.com
```

## 📊 Service URLs (Production)

| Service | URL | Status |
|---------|-----|--------|
| Auth Service | https://auth-service-new-builder.vercel.app | ✅ Ready |
| Builder API | https://builder-api-new-builder.vercel.app | ✅ Ready |
| Builder Editor | https://builder-editor-new-builder.vercel.app | 🔄 Generated |
| Templates Service | https://templates-service-new-builder.vercel.app | 🔄 Generated |
| Media Service | https://media-service-new-builder.vercel.app | 🔄 Generated |
| Publish Service | https://publish-service-new-builder.vercel.app | 🔄 Generated |
| Domain Service | https://domain-service-new-builder.vercel.app | 🔄 Generated |
| Billing Service | https://billing-service-new-builder.vercel.app | 🔄 Generated |
| Analytics Service | https://analytics-service-new-builder.vercel.app | 🔄 Generated |
| Questionnaire Service | https://questionnaire-service-new-builder.vercel.app | 🔄 Generated |
| Invitation Service | https://invitation-service-new-builder.vercel.app | 🔄 Generated |
| Geo Service | https://geo-service-new-builder.vercel.app | 🔄 Generated |
| CRM Integration | https://crm-integration-new-builder.vercel.app | 🔄 Generated |
| Admin Dashboard | https://admin-dashboard-new-builder.vercel.app | 🔄 Generated |
| Backoffice API | https://backoffice-api-new-builder.vercel.app | 🔄 Generated |
| Backoffice Dashboard | https://backoffice-dashboard-new-builder.vercel.app | 🔄 Generated |
| Site Dashboard | https://site-dashboard-new-builder.vercel.app | 🔄 Generated |
| Test Service | https://test-service-new-builder.vercel.app | 🔄 Generated |

## 🛠️ Development Setup

### Prerequisites
- Node.js 18+
- npm or yarn
- Supabase account
- Vercel account

### Local Development
```bash
# Clone repository
git clone [repository-url]
cd new-builder

# Install dependencies for a service
cd Micro-Services/auth-service
npm install

# Set up environment variables
cp .env.example .env
# Edit .env with your configuration

# Start development server
npm run dev

# Run tests
npm test
```

### Deployment
```bash
# Make deployment script executable
chmod +x deploy-all-services.sh

# Deploy all services
./deploy-all-services.sh
```

## 🧪 Testing Strategy

### Unit Testing
- **Framework**: Vitest
- **Coverage**: All business logic
- **Mocking**: Database and external services

### Integration Testing
- **API Testing**: Supertest for endpoint testing
- **Database Testing**: Test database with sample data
- **Authentication Testing**: JWT token validation

### End-to-End Testing
- **User Flows**: Complete user journeys
- **Cross-Service Testing**: Service communication
- **Performance Testing**: Load and stress testing

## 📈 Monitoring & Observability

### Health Checks
- All services expose `/health` endpoints
- Service status monitoring
- Uptime tracking

### Logging
- Structured logging with timestamps
- Request/response logging
- Error tracking and alerting

### Metrics
- API response times
- Database query performance
- User activity tracking
- System resource usage

## 🔄 API Standards

### Response Format
```json
{
  "success": true,
  "data": {
    // Response data
  },
  "message": "Operation completed successfully",
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100,
    "totalPages": 5
  }
}
```

### Error Format
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human-readable error message",
    "details": [
      {
        "field": "email",
        "message": "Invalid email format"
      }
    ]
  }
}
```

## 🚀 Next Steps

### Immediate Actions
1. **Complete Service Implementation**: Finish business logic for generated services
2. **Environment Configuration**: Set up production environment variables
3. **Integration Testing**: Test service-to-service communication
4. **Performance Optimization**: Optimize database queries and API responses

### Medium-term Goals
1. **Frontend Integration**: Connect frontend applications to APIs
2. **Advanced Features**: Implement advanced builder features
3. **Third-party Integrations**: Connect external services
4. **Monitoring Setup**: Implement comprehensive monitoring

### Long-term Vision
1. **Scalability**: Auto-scaling and load balancing
2. **Global Distribution**: Multi-region deployment
3. **Advanced Analytics**: Machine learning insights
4. **Enterprise Features**: Advanced security and compliance

## 📞 Support & Maintenance

### Development Team Contacts
- **Architecture**: System design and service communication
- **Backend**: API development and database management
- **Frontend**: User interface and user experience
- **DevOps**: Deployment and infrastructure management

### Documentation Updates
This documentation should be updated with each major release and architectural change. All team members are responsible for keeping their service documentation current.

---

**Last Updated**: June 2, 2025
**Version**: 1.0.0
**Status**: Production Ready (Core Services), Development (Extended Services)

