'use client'

import React, { useState } from 'react'
import {
  Box,
  VStack,
  HStack,
  Text,
  IconButton,
  Button,
  Tooltip,
  useColorModeValue,
  Badge,
  Divider,

  Collapse,
  useDisclosure
} from '@chakra-ui/react'
import {
  ChevronUpIcon,
  ChevronDownIcon,
  RepeatIcon,
  DeleteIcon,
  AddIcon,
  EditIcon,
  DragHandleIcon,
  TimeIcon
} from '@chakra-ui/icons'
import { useEditorStore, EditorAction } from '@/lib/stores/editorStore'

export function HistoryPanel() {
  const [expandedActions, setExpandedActions] = useState<Set<number>>(new Set())
  const { isOpen, onToggle } = useDisclosure({ defaultIsOpen: true })
  
  const borderColor = useColorModeValue('gray.200', 'gray.700')
  const bgColor = useColorModeValue('white', 'gray.800')
  const hoverBg = useColorModeValue('gray.50', 'gray.700')
  
  const {
    undoStack,
    redoStack,
    undo,
    redo
  } = useEditorStore()

  const toggleActionExpansion = (index: number) => {
    const newExpanded = new Set(expandedActions)
    if (newExpanded.has(index)) {
      newExpanded.delete(index)
    } else {
      newExpanded.add(index)
    }
    setExpandedActions(newExpanded)
  }

  const getActionIcon = (action: EditorAction) => {
    switch (action.type) {
      case 'add':
        return <AddIcon color="green.500" />
      case 'update':
        return <EditIcon color="blue.500" />
      case 'delete':
        return <DeleteIcon color="red.500" />
      case 'move':
        return <DragHandleIcon color="purple.500" />
      default:
        return <EditIcon color="gray.500" />
    }
  }

  const getActionDescription = (action: EditorAction) => {
    const target = action.target === 'element' ? 'Element' : 'Section'
    
    switch (action.type) {
      case 'add':
        if (action.data.elements && action.data.elements.length > 1) {
          return `Added ${action.data.elements.length} elements`
        }
        return `Added ${target}`
      case 'update':
        if (action.data.updates && action.data.updates.length > 1) {
          return `Updated ${action.data.updates.length} elements`
        }
        return `Updated ${target}`
      case 'delete':
        if (action.data.deletedElements && action.data.deletedElements.length > 1) {
          return `Deleted ${action.data.deletedElements.length} elements`
        }
        return `Deleted ${target}`
      case 'move':
        return `Moved ${target}`
      default:
        return `Modified ${target}`
    }
  }

  const getActionDetails = (action: EditorAction) => {
    const details = []
    
    if (action.data.id) {
      details.push(`ID: ${action.data.id}`)
    }
    
    if (action.data.element?.type) {
      details.push(`Type: ${action.data.element.type}`)
    }
    
    if (action.data.props) {
      const propKeys = Object.keys(action.data.props)
      if (propKeys.length > 0) {
        details.push(`Properties: ${propKeys.join(', ')}`)
      }
    }
    
    if (action.data.updates) {
      details.push(`Batch update: ${action.data.updates.length} items`)
    }
    
    return details
  }

  const formatTimestamp = (timestamp: number) => {
    const now = Date.now()
    const diff = now - timestamp
    
    if (diff < 60000) { // Less than 1 minute
      return 'Just now'
    } else if (diff < 3600000) { // Less than 1 hour
      const minutes = Math.floor(diff / 60000)
      return `${minutes}m ago`
    } else if (diff < 86400000) { // Less than 1 day
      const hours = Math.floor(diff / 3600000)
      return `${hours}h ago`
    } else {
      return new Date(timestamp).toLocaleDateString()
    }
  }

  const renderAction = (action: EditorAction, index: number, isRedo = false) => {
    const isExpanded = expandedActions.has(index)
    const details = getActionDetails(action)
    
    return (
      <Box
        key={`${isRedo ? 'redo' : 'undo'}-${index}`}
        p={2}
        border="1px"
        borderColor={borderColor}
        borderRadius="md"
        bg={isRedo ? 'blue.50' : bgColor}
        _hover={{ bg: hoverBg }}
        opacity={isRedo ? 0.7 : 1}
      >
        <HStack justify="space-between" align="start">
          <HStack spacing={2} flex={1}>
            {getActionIcon(action)}
            <VStack align="start" spacing={0} flex={1}>
              <Text fontSize="sm" fontWeight="medium">
                {getActionDescription(action)}
              </Text>
              <HStack spacing={2}>
                <Text fontSize="xs" color="gray.500">
                  {formatTimestamp(action.timestamp)}
                </Text>
                <Badge size="sm" colorScheme={action.type === 'add' ? 'green' : action.type === 'delete' ? 'red' : 'blue'}>
                  {action.type}
                </Badge>
              </HStack>
            </VStack>
          </HStack>
          
          {details.length > 0 && (
            <IconButton
              aria-label="Toggle details"
              icon={isExpanded ? <ChevronUpIcon /> : <ChevronDownIcon />}
              size="xs"
              variant="ghost"
              onClick={() => toggleActionExpansion(index)}
            />
          )}
        </HStack>
        
        {details.length > 0 && (
          <Collapse in={isExpanded}>
            <Box mt={2} pt={2} borderTop="1px" borderColor={borderColor}>
              <VStack align="start" spacing={1}>
                {details.map((detail, detailIndex) => (
                  <Text key={detailIndex} fontSize="xs" color="gray.600">
                    {detail}
                  </Text>
                ))}
              </VStack>
            </Box>
          </Collapse>
        )}
      </Box>
    )
  }

  return (
    <Box
      w="300px"
      h="100%"
      bg={bgColor}
      borderLeft="1px"
      borderColor={borderColor}
      display="flex"
      flexDirection="column"
    >
      {/* Header */}
      <HStack justify="space-between" p={3} borderBottom="1px" borderColor={borderColor}>
        <HStack>
          <TimeIcon />
          <Text fontSize="sm" fontWeight="semibold">
            History
          </Text>
        </HStack>
        <IconButton
          aria-label="Toggle history panel"
          icon={isOpen ? <ChevronUpIcon /> : <ChevronDownIcon />}
          size="xs"
          variant="ghost"
          onClick={onToggle}
        />
      </HStack>

      <Collapse in={isOpen}>
        {/* Controls */}
        <HStack p={3} spacing={2}>
          <Button
            size="sm"
            leftIcon={<RepeatIcon transform="scaleX(-1)" />}
            onClick={undo}
            isDisabled={undoStack.length === 0}
            flex={1}
          >
            Undo
          </Button>
          <Button
            size="sm"
            leftIcon={<RepeatIcon />}
            onClick={redo}
            isDisabled={redoStack.length === 0}
            flex={1}
          >
            Redo
          </Button>
        </HStack>

        <Divider />

        {/* History List */}
        <Box flex={1} overflowY="auto">
          <VStack spacing={2} p={3} align="stretch">
            {/* Redo Stack (future actions) */}
            {redoStack.length > 0 && (
              <>
                <Text fontSize="xs" fontWeight="semibold" color="blue.600" mb={1}>
                  REDO AVAILABLE ({redoStack.length})
                </Text>
                {redoStack.slice().reverse().map((action, index) => 
                  renderAction(action, redoStack.length - 1 - index, true)
                )}
                <Divider my={2} />
              </>
            )}

            {/* Current State Indicator */}
            <Box
              p={2}
              bg="green.50"
              border="2px"
              borderColor="green.200"
              borderRadius="md"
              textAlign="center"
            >
              <Text fontSize="xs" fontWeight="semibold" color="green.700">
                CURRENT STATE
              </Text>
            </Box>

            {/* Undo Stack (past actions) */}
            {undoStack.length > 0 ? (
              <>
                <Text fontSize="xs" fontWeight="semibold" color="gray.600" mt={2} mb={1}>
                  HISTORY ({undoStack.length})
                </Text>
                {undoStack.slice().reverse().map((action, index) => 
                  renderAction(action, undoStack.length - 1 - index, false)
                )}
              </>
            ) : (
              <Box p={4} textAlign="center">
                <Text fontSize="sm" color="gray.500">
                  No history yet. Start editing to see your actions here.
                </Text>
              </Box>
            )}
          </VStack>
        </Box>

        {/* Footer Stats */}
        <Box p={3} borderTop="1px" borderColor={borderColor} bg="gray.50">
          <HStack justify="space-between">
            <Text fontSize="xs" color="gray.600">
              {undoStack.length} actions
            </Text>
            <Text fontSize="xs" color="gray.600">
              {redoStack.length} redo available
            </Text>
          </HStack>
        </Box>
      </Collapse>
    </Box>
  )
}

export default HistoryPanel
