'use client'

import {
  Box,
  VStack,
  Text,
  Input,
  FormControl,
  FormLabel,
  Button,
  Divider,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  useColorModeValue
} from '@chakra-ui/react'
import { Section, useEditorStore } from '@/lib/stores/editorStore'

interface SectionPropertiesProps {
  section: Section
}

export function SectionProperties({ section }: SectionPropertiesProps) {
  const borderColor = useColorModeValue('gray.200', 'gray.700')
  const { updateSection, deleteSection, currentBreakpoint } = useEditorStore()

  const handleNameChange = (name: string) => {
    updateSection(section.id, { name })
  }

  const handleStyleChange = (key: string, value: any) => {
    updateSection(section.id, {
      style: {
        ...section.style,
        [key]: value
      }
    })
  }

  const handleResponsiveStyleChange = (key: string, value: any) => {
    updateSection(section.id, {
      responsive: {
        ...section.responsive,
        [currentBreakpoint]: {
          ...section.responsive[currentBreakpoint],
          [key]: value
        }
      }
    })
  }

  return (
    <Box p={4}>
      <VStack spacing={4} align="stretch">
        {/* Section Header */}
        <Box>
          <Text fontSize="sm" fontWeight="semibold" color="gray.700" mb={1}>
            {section.type.charAt(0).toUpperCase() + section.type.slice(1)} Section
          </Text>
          <Text fontSize="xs" color="gray.500">
            ID: {section.id}
          </Text>
        </Box>

        <Divider />

        {/* Section Name */}
        <FormControl>
          <FormLabel fontSize="sm">Section Name</FormLabel>
          <Input
            value={section.name}
            onChange={(e) => handleNameChange(e.target.value)}
            size="sm"
            placeholder="Section name"
          />
        </FormControl>

        <Divider />

        {/* Section Properties */}
        <Accordion allowToggle defaultIndex={[0]}>
          <AccordionItem border="none">
            <AccordionButton px={0}>
              <Box flex="1" textAlign="left">
                <Text fontSize="sm" fontWeight="medium">Layout & Spacing</Text>
              </Box>
              <AccordionIcon />
            </AccordionButton>
            <AccordionPanel px={0} pb={4}>
              <VStack spacing={3} align="stretch">
                <FormControl>
                  <FormLabel fontSize="sm">Padding</FormLabel>
                  <Input
                    value={section.style.padding || '60px 20px'}
                    onChange={(e) => handleStyleChange('padding', e.target.value)}
                    size="sm"
                    placeholder="60px 20px"
                  />
                </FormControl>
                
                <FormControl>
                  <FormLabel fontSize="sm">Margin</FormLabel>
                  <Input
                    value={section.style.margin || '0'}
                    onChange={(e) => handleStyleChange('margin', e.target.value)}
                    size="sm"
                    placeholder="0"
                  />
                </FormControl>
                
                <FormControl>
                  <FormLabel fontSize="sm">Min Height</FormLabel>
                  <Input
                    value={section.style.minHeight || 'auto'}
                    onChange={(e) => handleStyleChange('minHeight', e.target.value)}
                    size="sm"
                    placeholder="auto"
                  />
                </FormControl>
                
                <FormControl>
                  <FormLabel fontSize="sm">Max Width</FormLabel>
                  <Input
                    value={section.style.maxWidth || '1200px'}
                    onChange={(e) => handleStyleChange('maxWidth', e.target.value)}
                    size="sm"
                    placeholder="1200px"
                  />
                </FormControl>
              </VStack>
            </AccordionPanel>
          </AccordionItem>

          <AccordionItem border="none">
            <AccordionButton px={0}>
              <Box flex="1" textAlign="left">
                <Text fontSize="sm" fontWeight="medium">Background</Text>
              </Box>
              <AccordionIcon />
            </AccordionButton>
            <AccordionPanel px={0} pb={4}>
              <VStack spacing={3} align="stretch">
                <FormControl>
                  <FormLabel fontSize="sm">Background Color</FormLabel>
                  <Input
                    type="color"
                    value={section.style.backgroundColor || '#ffffff'}
                    onChange={(e) => handleStyleChange('backgroundColor', e.target.value)}
                    size="sm"
                  />
                </FormControl>
                
                <FormControl>
                  <FormLabel fontSize="sm">Background Image</FormLabel>
                  <Input
                    value={section.style.backgroundImage || ''}
                    onChange={(e) => handleStyleChange('backgroundImage', e.target.value)}
                    size="sm"
                    placeholder="url(image.jpg)"
                  />
                </FormControl>
                
                <FormControl>
                  <FormLabel fontSize="sm">Background Size</FormLabel>
                  <Input
                    value={section.style.backgroundSize || 'cover'}
                    onChange={(e) => handleStyleChange('backgroundSize', e.target.value)}
                    size="sm"
                    placeholder="cover"
                  />
                </FormControl>
                
                <FormControl>
                  <FormLabel fontSize="sm">Background Position</FormLabel>
                  <Input
                    value={section.style.backgroundPosition || 'center'}
                    onChange={(e) => handleStyleChange('backgroundPosition', e.target.value)}
                    size="sm"
                    placeholder="center"
                  />
                </FormControl>
              </VStack>
            </AccordionPanel>
          </AccordionItem>

          <AccordionItem border="none">
            <AccordionButton px={0}>
              <Box flex="1" textAlign="left">
                <Text fontSize="sm" fontWeight="medium">Typography</Text>
              </Box>
              <AccordionIcon />
            </AccordionButton>
            <AccordionPanel px={0} pb={4}>
              <VStack spacing={3} align="stretch">
                <FormControl>
                  <FormLabel fontSize="sm">Text Color</FormLabel>
                  <Input
                    type="color"
                    value={section.style.color || '#000000'}
                    onChange={(e) => handleStyleChange('color', e.target.value)}
                    size="sm"
                  />
                </FormControl>
                
                <FormControl>
                  <FormLabel fontSize="sm">Text Align</FormLabel>
                  <Input
                    value={section.style.textAlign || 'left'}
                    onChange={(e) => handleStyleChange('textAlign', e.target.value)}
                    size="sm"
                    placeholder="left"
                  />
                </FormControl>
                
                <FormControl>
                  <FormLabel fontSize="sm">Font Family</FormLabel>
                  <Input
                    value={section.style.fontFamily || 'inherit'}
                    onChange={(e) => handleStyleChange('fontFamily', e.target.value)}
                    size="sm"
                    placeholder="inherit"
                  />
                </FormControl>
              </VStack>
            </AccordionPanel>
          </AccordionItem>

          <AccordionItem border="none">
            <AccordionButton px={0}>
              <Box flex="1" textAlign="left">
                <Text fontSize="sm" fontWeight="medium">
                  Responsive ({currentBreakpoint})
                </Text>
              </Box>
              <AccordionIcon />
            </AccordionButton>
            <AccordionPanel px={0} pb={4}>
              <VStack spacing={3} align="stretch">
                <Text fontSize="xs" color="gray.500" mb={2}>
                  Override styles for {currentBreakpoint} devices
                </Text>
                
                <FormControl>
                  <FormLabel fontSize="sm">Padding ({currentBreakpoint})</FormLabel>
                  <Input
                    value={section.responsive[currentBreakpoint]?.padding || ''}
                    onChange={(e) => handleResponsiveStyleChange('padding', e.target.value)}
                    size="sm"
                    placeholder="Override padding"
                  />
                </FormControl>
                
                <FormControl>
                  <FormLabel fontSize="sm">Text Align ({currentBreakpoint})</FormLabel>
                  <Input
                    value={section.responsive[currentBreakpoint]?.textAlign || ''}
                    onChange={(e) => handleResponsiveStyleChange('textAlign', e.target.value)}
                    size="sm"
                    placeholder="Override text align"
                  />
                </FormControl>
                
                <FormControl>
                  <FormLabel fontSize="sm">Display ({currentBreakpoint})</FormLabel>
                  <Input
                    value={section.responsive[currentBreakpoint]?.display || ''}
                    onChange={(e) => handleResponsiveStyleChange('display', e.target.value)}
                    size="sm"
                    placeholder="block, none, flex, etc."
                  />
                </FormControl>
              </VStack>
            </AccordionPanel>
          </AccordionItem>
        </Accordion>

        <Divider />

        {/* Actions */}
        <Button
          colorScheme="red"
          variant="outline"
          size="sm"
          onClick={() => deleteSection(section.id)}
        >
          Delete Section
        </Button>
      </VStack>
    </Box>
  )
}
