import { describe, it, expect } from 'vitest'
import request from 'supertest'
import app from '../src/index'

describe('geo-service', () => {
  it('should return health status', async () => {
    const response = await request(app)
      .get('/health')
      .expect(200)
    
    expect(response.body.status).toBe('healthy')
    expect(response.body.service).toBe('geo-service')
  })
  
  it('should return service info', async () => {
    const response = await request(app)
      .get('/')
      .expect(200)
    
    expect(response.body.success).toBe(true)
    expect(response.body.data.service).toBe('geo-service')
  })
})
