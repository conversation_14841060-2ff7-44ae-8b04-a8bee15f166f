'use client'

import React, { useState } from 'react'
import {
  Box,
  VStack,
  HStack,
  Text,
  Input,
  Select,
  Button,
  FormControl,
  FormLabel,
  Switch,
  Slider,
  SliderTrack,
  SliderFilledTrack,
  SliderThumb,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  useColorModeValue
} from '@chakra-ui/react'

export interface BackgroundSettings {
  type: 'color' | 'image' | 'video' | 'gradient'
  color?: string
  image?: {
    url: string
    position: string
    size: string
    repeat: string
    attachment: string
    overlay?: {
      enabled: boolean
      color: string
      opacity: number
    }
  }
  video?: {
    url: string
    poster?: string
    autoplay: boolean
    loop: boolean
    muted: boolean
    overlay?: {
      enabled: boolean
      color: string
      opacity: number
    }
  }
  gradient?: {
    type: 'linear' | 'radial'
    direction: string
    colors: Array<{
      color: string
      position: number
    }>
  }
}

interface BackgroundContainerProps {
  children: React.ReactNode
  settings: BackgroundSettings
  onChange?: (settings: BackgroundSettings) => void
  isEditing?: boolean
  className?: string
  style?: React.CSSProperties
}

export function BackgroundContainer({
  children,
  settings,
  onChange,
  isEditing = false,
  className,
  style
}: BackgroundContainerProps) {
  const [localSettings, setLocalSettings] = useState<BackgroundSettings>(settings)

  const handleSettingsChange = (newSettings: Partial<BackgroundSettings>) => {
    const updated = { ...localSettings, ...newSettings }
    setLocalSettings(updated)
    onChange?.(updated)
  }

  const getBackgroundStyle = (): React.CSSProperties => {
    const baseStyle: React.CSSProperties = {
      position: 'relative',
      overflow: 'hidden',
      ...style
    }

    switch (localSettings.type) {
      case 'color':
        return {
          ...baseStyle,
          backgroundColor: localSettings.color || '#ffffff'
        }

      case 'image':
        if (localSettings.image) {
          return {
            ...baseStyle,
            backgroundImage: `url(${localSettings.image.url})`,
            backgroundPosition: localSettings.image.position || 'center',
            backgroundSize: localSettings.image.size || 'cover',
            backgroundRepeat: localSettings.image.repeat || 'no-repeat',
            backgroundAttachment: localSettings.image.attachment || 'scroll'
          }
        }
        break

      case 'video':
        return {
          ...baseStyle,
          backgroundColor: '#000000'
        }

      case 'gradient':
        if (localSettings.gradient) {
          const { type, direction, colors } = localSettings.gradient
          const colorStops = colors
            .map(c => `${c.color} ${c.position}%`)
            .join(', ')
          
          const gradientValue = type === 'linear'
            ? `linear-gradient(${direction}, ${colorStops})`
            : `radial-gradient(circle, ${colorStops})`
          
          return {
            ...baseStyle,
            background: gradientValue
          }
        }
        break
    }

    return baseStyle
  }

  const renderOverlay = () => {
    const overlay = localSettings.type === 'image' 
      ? localSettings.image?.overlay 
      : localSettings.video?.overlay

    if (!overlay?.enabled) return null

    return (
      <Box
        position="absolute"
        top="0"
        left="0"
        right="0"
        bottom="0"
        backgroundColor={overlay.color}
        opacity={overlay.opacity / 100}
        zIndex={1}
      />
    )
  }

  const renderVideo = () => {
    if (localSettings.type !== 'video' || !localSettings.video) return null

    return (
      <Box
        as="video"
        position="absolute"
        top="0"
        left="0"
        width="100%"
        height="100%"
        objectFit="cover"
        zIndex={0}
        autoPlay={localSettings.video.autoplay}
        loop={localSettings.video.loop}
        muted={localSettings.video.muted}
        poster={localSettings.video.poster}
      >
        <source src={localSettings.video.url} type="video/mp4" />
      </Box>
    )
  }

  return (
    <Box
      className={className}
      style={getBackgroundStyle()}
    >
      {renderVideo()}
      {renderOverlay()}
      
      {/* Content */}
      <Box position="relative" zIndex={2}>
        {children}
      </Box>
    </Box>
  )
}

// Background Settings Editor Component
export function BackgroundEditor({
  settings,
  onChange
}: {
  settings: BackgroundSettings
  onChange: (settings: BackgroundSettings) => void
}) {
  const borderColor = useColorModeValue('gray.200', 'gray.700')

  const handleTypeChange = (type: BackgroundSettings['type']) => {
    const newSettings: BackgroundSettings = { type }
    
    switch (type) {
      case 'color':
        newSettings.color = '#ffffff'
        break
      case 'image':
        newSettings.image = {
          url: '',
          position: 'center',
          size: 'cover',
          repeat: 'no-repeat',
          attachment: 'scroll'
        }
        break
      case 'video':
        newSettings.video = {
          url: '',
          autoplay: true,
          loop: true,
          muted: true
        }
        break
      case 'gradient':
        newSettings.gradient = {
          type: 'linear',
          direction: '45deg',
          colors: [
            { color: '#ffffff', position: 0 },
            { color: '#000000', position: 100 }
          ]
        }
        break
    }
    
    onChange(newSettings)
  }

  return (
    <VStack spacing={4} align="stretch" p={4} border="1px" borderColor={borderColor} borderRadius="md">
      <Text fontSize="sm" fontWeight="semibold">Background Settings</Text>
      
      <FormControl>
        <FormLabel fontSize="sm">Background Type</FormLabel>
        <Select
          value={settings.type}
          onChange={(e) => handleTypeChange(e.target.value as BackgroundSettings['type'])}
          size="sm"
        >
          <option value="color">Solid Color</option>
          <option value="image">Image</option>
          <option value="video">Video</option>
          <option value="gradient">Gradient</option>
        </Select>
      </FormControl>

      <Tabs size="sm" variant="enclosed">
        <TabList>
          <Tab>Settings</Tab>
          <Tab>Overlay</Tab>
        </TabList>
        
        <TabPanels>
          <TabPanel px={0}>
            {settings.type === 'color' && (
              <FormControl>
                <FormLabel fontSize="sm">Color</FormLabel>
                <Input
                  type="color"
                  value={settings.color || '#ffffff'}
                  onChange={(e) => onChange({ ...settings, color: e.target.value })}
                  size="sm"
                />
              </FormControl>
            )}
            
            {settings.type === 'image' && settings.image && (
              <VStack spacing={3} align="stretch">
                <FormControl>
                  <FormLabel fontSize="sm">Image URL</FormLabel>
                  <Input
                    value={settings.image.url}
                    onChange={(e) => onChange({
                      ...settings,
                      image: { ...settings.image!, url: e.target.value }
                    })}
                    size="sm"
                    placeholder="https://example.com/image.jpg"
                  />
                </FormControl>
                
                <FormControl>
                  <FormLabel fontSize="sm">Position</FormLabel>
                  <Select
                    value={settings.image.position}
                    onChange={(e) => onChange({
                      ...settings,
                      image: { ...settings.image!, position: e.target.value }
                    })}
                    size="sm"
                  >
                    <option value="center">Center</option>
                    <option value="top">Top</option>
                    <option value="bottom">Bottom</option>
                    <option value="left">Left</option>
                    <option value="right">Right</option>
                  </Select>
                </FormControl>
                
                <FormControl>
                  <FormLabel fontSize="sm">Size</FormLabel>
                  <Select
                    value={settings.image.size}
                    onChange={(e) => onChange({
                      ...settings,
                      image: { ...settings.image!, size: e.target.value }
                    })}
                    size="sm"
                  >
                    <option value="cover">Cover</option>
                    <option value="contain">Contain</option>
                    <option value="auto">Auto</option>
                    <option value="100% 100%">Stretch</option>
                  </Select>
                </FormControl>
              </VStack>
            )}
          </TabPanel>
          
          <TabPanel px={0}>
            {(settings.type === 'image' || settings.type === 'video') && (
              <VStack spacing={3} align="stretch">
                <FormControl display="flex" alignItems="center">
                  <FormLabel fontSize="sm" mb="0">Enable Overlay</FormLabel>
                  <Switch
                    isChecked={
                      settings.type === 'image' 
                        ? settings.image?.overlay?.enabled 
                        : settings.video?.overlay?.enabled
                    }
                    onChange={(e) => {
                      const overlaySettings = {
                        enabled: e.target.checked,
                        color: '#000000',
                        opacity: 50
                      }
                      
                      if (settings.type === 'image') {
                        onChange({
                          ...settings,
                          image: {
                            ...settings.image!,
                            overlay: overlaySettings
                          }
                        })
                      } else {
                        onChange({
                          ...settings,
                          video: {
                            ...settings.video!,
                            overlay: overlaySettings
                          }
                        })
                      }
                    }}
                    size="sm"
                  />
                </FormControl>
              </VStack>
            )}
          </TabPanel>
        </TabPanels>
      </Tabs>
    </VStack>
  )
}

export default BackgroundContainer
