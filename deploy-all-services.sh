#!/bin/bash

# Deploy All Microservices to Vercel
# This script deploys all 18 microservices to Vercel with proper configuration

set -e

echo "🚀 Starting deployment of all microservices..."

# Vercel configuration
VERCEL_TOKEN="************************"
VERCEL_PROJECT_ID="prj_eW86m1NmWx2W3IAZGcHWL12Lz0HA"

# Supabase configuration
SUPABASE_URL="https://cikzkzviubwpruiowapp.supabase.co"
SUPABASE_PROJECT_ID="cikzkzviubwpruiowapp"

# Services to deploy
SERVICES=(
  "auth-service"
  "builder-api"
  "builder-editor"
  "templates-service"
  "media-service"
  "publish-service"
  "domain-service"
  "billing-service"
  "analytics-service"
  "questionnaire-service"
  "invitation-service"
  "geo-service"
  "crm-integration"
  "admin-dashboard"
  "backoffice-api"
  "backoffice-dashboard"
  "site-dashboard"
  "test-service"
)

# Function to deploy a service
deploy_service() {
  local service=$1
  echo "📦 Deploying $service..."
  
  cd "Micro-Services/$service"
  
  # Set environment variables for the service
  vercel env add SUPABASE_URL "$SUPABASE_URL" production --token "$VERCEL_TOKEN" --yes || true
  vercel env add SUPABASE_PROJECT_ID "$SUPABASE_PROJECT_ID" production --token "$VERCEL_TOKEN" --yes || true
  vercel env add NODE_ENV "production" production --token "$VERCEL_TOKEN" --yes || true
  
  # Deploy to Vercel
  vercel --prod --token "$VERCEL_TOKEN" --confirm
  
  echo "✅ $service deployed successfully"
  cd ../..
}

# Deploy each service
for service in "${SERVICES[@]}"; do
  if [ -d "Micro-Services/$service" ]; then
    deploy_service "$service"
  else
    echo "⚠️  Service directory not found: $service"
  fi
done

echo "🎉 All services deployed successfully!"
echo ""
echo "📋 Service URLs:"
echo "🔐 Auth Service: https://auth-service-new-builder.vercel.app"
echo "🏗️ Builder API: https://builder-api-new-builder.vercel.app"
echo "📝 Builder Editor: https://builder-editor-new-builder.vercel.app"
echo "📄 Templates Service: https://templates-service-new-builder.vercel.app"
echo "📁 Media Service: https://media-service-new-builder.vercel.app"
echo "🚀 Publish Service: https://publish-service-new-builder.vercel.app"
echo "🌐 Domain Service: https://domain-service-new-builder.vercel.app"
echo "💰 Billing Service: https://billing-service-new-builder.vercel.app"
echo "📊 Analytics Service: https://analytics-service-new-builder.vercel.app"
echo "📋 Questionnaire Service: https://questionnaire-service-new-builder.vercel.app"
echo "👥 Invitation Service: https://invitation-service-new-builder.vercel.app"
echo "🌍 Geo Service: https://geo-service-new-builder.vercel.app"
echo "🔗 CRM Integration: https://crm-integration-new-builder.vercel.app"
echo "👑 Admin Dashboard: https://admin-dashboard-new-builder.vercel.app"
echo "🏢 Backoffice API: https://backoffice-api-new-builder.vercel.app"
echo "🏢 Backoffice Dashboard: https://backoffice-dashboard-new-builder.vercel.app"
echo "📊 Site Dashboard: https://site-dashboard-new-builder.vercel.app"
echo "🧪 Test Service: https://test-service-new-builder.vercel.app"

