import { describe, it, expect } from 'vitest'
import { generateTokens, verifyToken, validatePassword } from '../src/lib/authHelpers'

describe('Auth Helpers', () => {
  const mockUser = {
    id: '123e4567-e89b-12d3-a456-426614174000',
    email: '<EMAIL>',
    role: 'user'
  }

  describe('generateTokens', () => {
    it('should generate valid access and refresh tokens', () => {
      const tokens = generateTokens(mockUser)
      
      expect(tokens.accessToken).toBeDefined()
      expect(tokens.refreshToken).toBeDefined()
      expect(typeof tokens.accessToken).toBe('string')
      expect(typeof tokens.refreshToken).toBe('string')
    })
  })

  describe('verifyToken', () => {
    it('should verify valid token', () => {
      const tokens = generateTokens(mockUser)
      const decoded = verifyToken(tokens.accessToken)
      
      expect(decoded.id).toBe(mockUser.id)
      expect(decoded.email).toBe(mockUser.email)
      expect(decoded.role).toBe(mockUser.role)
    })

    it('should throw error for invalid token', () => {
      expect(() => verifyToken('invalid-token')).toThrow('Invalid or expired token')
    })
  })

  describe('validatePassword', () => {
    it('should validate strong password', () => {
      expect(validatePassword('StrongPass123')).toBe(true)
    })

    it('should reject weak passwords', () => {
      expect(validatePassword('weak')).toBe(false) // too short
      expect(validatePassword('weakpassword')).toBe(false) // no uppercase or numbers
      expect(validatePassword('WEAKPASSWORD')).toBe(false) // no lowercase or numbers
      expect(validatePassword('WeakPassword')).toBe(false) // no numbers
    })
  })
})

