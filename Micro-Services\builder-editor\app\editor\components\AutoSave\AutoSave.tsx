'use client'

import React, { useEffect, useState, useCallback } from 'react'
import {
  Box,
  HStack,
  Text,
  Spinner,
  Icon,
  useToast,
  useColorModeValue
} from '@chakra-ui/react'
import { CheckIcon, WarningIcon, TimeIcon } from '@chakra-ui/icons'
import { useEditorStore } from '@/lib/stores/editorStore'

interface AutoSaveProps {
  interval?: number // Auto-save interval in milliseconds
  enabled?: boolean
}

type SaveStatus = 'idle' | 'saving' | 'saved' | 'error' | 'pending'

export function AutoSave({ interval = 30000, enabled = true }: AutoSaveProps) {
  const [saveStatus, setSaveStatus] = useState<SaveStatus>('idle')
  const [lastSaved, setLastSaved] = useState<Date | null>(null)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [saveError, setSaveError] = useState<string | null>(null)
  
  const toast = useToast()
  const bgColor = useColorModeValue('white', 'gray.800')
  const borderColor = useColorModeValue('gray.200', 'gray.700')
  
  const {
    currentPage,
    undoStack,
    addToHistory
  } = useEditorStore()

  // Track changes by monitoring the undo stack
  useEffect(() => {
    if (undoStack.length > 0) {
      const lastAction = undoStack[undoStack.length - 1]
      const timeSinceLastSave = lastSaved ? Date.now() - lastSaved.getTime() : Infinity
      
      // Mark as having unsaved changes if there's been an action since last save
      if (timeSinceLastSave > 1000) { // 1 second buffer
        setHasUnsavedChanges(true)
        setSaveStatus('pending')
      }
    }
  }, [undoStack, lastSaved])

  const saveToSupabase = useCallback(async () => {
    if (!currentPage) {
      throw new Error('No page to save')
    }

    // Simulate API call to Supabase
    // In real implementation, this would save to your Supabase database
    const response = await fetch('/api/pages/save', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        pageId: currentPage.id,
        pageData: currentPage,
        timestamp: new Date().toISOString()
      })
    })

    if (!response.ok) {
      throw new Error(`Save failed: ${response.statusText}`)
    }

    return await response.json()
  }, [currentPage])

  const performSave = useCallback(async () => {
    if (!hasUnsavedChanges || !currentPage || saveStatus === 'saving') {
      return
    }

    setSaveStatus('saving')
    setSaveError(null)

    try {
      await saveToSupabase()
      
      setLastSaved(new Date())
      setHasUnsavedChanges(false)
      setSaveStatus('saved')
      
      // Reset to idle after showing saved status
      setTimeout(() => {
        setSaveStatus('idle')
      }, 2000)
      
    } catch (error) {
      console.error('Auto-save failed:', error)
      setSaveError(error instanceof Error ? error.message : 'Save failed')
      setSaveStatus('error')
      
      // Show error toast
      toast({
        title: 'Auto-save failed',
        description: error instanceof Error ? error.message : 'Unknown error occurred',
        status: 'error',
        duration: 5000,
        isClosable: true,
      })
      
      // Reset to pending after showing error
      setTimeout(() => {
        setSaveStatus('pending')
      }, 3000)
    }
  }, [hasUnsavedChanges, currentPage, saveStatus, saveToSupabase, toast])

  // Auto-save interval
  useEffect(() => {
    if (!enabled) return

    const autoSaveInterval = setInterval(() => {
      if (hasUnsavedChanges && saveStatus !== 'saving') {
        performSave()
      }
    }, interval)

    return () => clearInterval(autoSaveInterval)
  }, [enabled, hasUnsavedChanges, saveStatus, interval, performSave])

  // Save on page unload
  useEffect(() => {
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        event.preventDefault()
        event.returnValue = 'You have unsaved changes. Are you sure you want to leave?'
        return event.returnValue
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    return () => window.removeEventListener('beforeunload', handleBeforeUnload)
  }, [hasUnsavedChanges])

  // Manual save function (can be called from keyboard shortcuts)
  const manualSave = useCallback(async () => {
    await performSave()
    
    if (saveStatus !== 'error') {
      toast({
        title: 'Page saved',
        description: 'Your changes have been saved successfully',
        status: 'success',
        duration: 2000,
        isClosable: true,
      })
    }
  }, [performSave, saveStatus, toast])

  // Expose manual save function globally
  useEffect(() => {
    (window as any).manualSave = manualSave
    return () => {
      delete (window as any).manualSave
    }
  }, [manualSave])

  const getStatusIcon = () => {
    switch (saveStatus) {
      case 'saving':
        return <Spinner size="xs" color="blue.500" />
      case 'saved':
        return <CheckIcon color="green.500" />
      case 'error':
        return <WarningIcon color="red.500" />
      case 'pending':
        return <TimeIcon color="orange.500" />
      default:
        return null
    }
  }

  const getStatusText = () => {
    switch (saveStatus) {
      case 'saving':
        return 'Saving...'
      case 'saved':
        return 'Saved'
      case 'error':
        return 'Save failed'
      case 'pending':
        return 'Unsaved changes'
      default:
        return lastSaved ? `Last saved ${formatLastSaved()}` : 'No changes'
    }
  }

  const formatLastSaved = () => {
    if (!lastSaved) return ''
    
    const now = new Date()
    const diff = now.getTime() - lastSaved.getTime()
    
    if (diff < 60000) { // Less than 1 minute
      return 'just now'
    } else if (diff < 3600000) { // Less than 1 hour
      const minutes = Math.floor(diff / 60000)
      return `${minutes}m ago`
    } else {
      return lastSaved.toLocaleTimeString([], { 
        hour: '2-digit', 
        minute: '2-digit' 
      })
    }
  }

  const getStatusColor = () => {
    switch (saveStatus) {
      case 'saving':
        return 'blue.600'
      case 'saved':
        return 'green.600'
      case 'error':
        return 'red.600'
      case 'pending':
        return 'orange.600'
      default:
        return 'gray.600'
    }
  }

  if (!enabled) return null

  return (
    <Box
      position="fixed"
      bottom={4}
      right={4}
      bg={bgColor}
      border="1px"
      borderColor={borderColor}
      borderRadius="md"
      px={3}
      py={2}
      shadow="md"
      zIndex={1000}
    >
      <HStack spacing={2}>
        {getStatusIcon()}
        <Text fontSize="xs" color={getStatusColor()} fontWeight="medium">
          {getStatusText()}
        </Text>
      </HStack>
      
      {saveError && (
        <Text fontSize="xs" color="red.500" mt={1}>
          {saveError}
        </Text>
      )}
    </Box>
  )
}

// Hook for manual save functionality
export const useManualSave = () => {
  const performSave = useCallback(() => {
    if ((window as any).manualSave) {
      (window as any).manualSave()
    }
  }, [])

  return { performSave }
}

export default AutoSave
