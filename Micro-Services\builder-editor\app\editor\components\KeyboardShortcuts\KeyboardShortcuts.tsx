'use client'

import React, { useEffect, useCallback } from 'react'
import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  VStack,
  HStack,
  Text,
  Kbd,
  Divider,
  useDisclosure,
  Box,
  useColorModeValue
} from '@chakra-ui/react'
import { useEditorStore } from '@/lib/stores/editorStore'

interface KeyboardShortcut {
  keys: string[]
  description: string
  action: () => void
  category: string
}

export function KeyboardShortcuts() {
  const { isOpen, onOpen, onClose } = useDisclosure()
  const borderColor = useColorModeValue('gray.200', 'gray.700')
  
  const {
    undo,
    redo,
    copyToClipboard,
    pasteFromClipboard,
    deleteElement,
    deleteMultipleElements,
    duplicateElement,
    selectedElement,
    selectedElements,
    setPreviewMode,
    isPreviewMode,
    clearSelection
  } = useEditorStore()

  const shortcuts: KeyboardShortcut[] = [
    // File Operations
    {
      keys: ['Ctrl', 'S'],
      description: 'Save page',
      action: () => {
        // TODO: Implement save functionality
        console.log('Save triggered')
      },
      category: 'File'
    },
    {
      keys: ['Ctrl', 'Shift', 'S'],
      description: 'Save as template',
      action: () => {
        // TODO: Implement save as template
        console.log('Save as template triggered')
      },
      category: 'File'
    },
    
    // Edit Operations
    {
      keys: ['Ctrl', 'Z'],
      description: 'Undo',
      action: undo,
      category: 'Edit'
    },
    {
      keys: ['Ctrl', 'Y'],
      description: 'Redo',
      action: redo,
      category: 'Edit'
    },
    {
      keys: ['Ctrl', 'C'],
      description: 'Copy selected elements',
      action: () => {
        if (selectedElements.length > 0) {
          copyToClipboard(selectedElements)
        } else if (selectedElement) {
          copyToClipboard([selectedElement])
        }
      },
      category: 'Edit'
    },
    {
      keys: ['Ctrl', 'V'],
      description: 'Paste elements',
      action: () => {
        // TODO: Get current section ID for pasting
        pasteFromClipboard()
      },
      category: 'Edit'
    },
    {
      keys: ['Ctrl', 'D'],
      description: 'Duplicate selected element',
      action: () => {
        if (selectedElement) {
          duplicateElement(selectedElement.id)
        }
      },
      category: 'Edit'
    },
    {
      keys: ['Delete'],
      description: 'Delete selected elements',
      action: () => {
        if (selectedElements.length > 1) {
          deleteMultipleElements(selectedElements.map(el => el.id))
        } else if (selectedElement) {
          deleteElement(selectedElement.id)
        }
      },
      category: 'Edit'
    },
    {
      keys: ['Escape'],
      description: 'Clear selection',
      action: clearSelection,
      category: 'Edit'
    },
    
    // View Operations
    {
      keys: ['Ctrl', 'Shift', 'P'],
      description: 'Toggle preview mode',
      action: () => setPreviewMode(!isPreviewMode),
      category: 'View'
    },
    {
      keys: ['?'],
      description: 'Show keyboard shortcuts',
      action: onOpen,
      category: 'Help'
    },
    
    // Selection Operations
    {
      keys: ['Ctrl', 'A'],
      description: 'Select all elements',
      action: () => {
        // TODO: Implement select all
        console.log('Select all triggered')
      },
      category: 'Selection'
    },
    
    // Navigation
    {
      keys: ['Tab'],
      description: 'Select next element',
      action: () => {
        // TODO: Implement tab navigation
        console.log('Tab navigation triggered')
      },
      category: 'Navigation'
    },
    {
      keys: ['Shift', 'Tab'],
      description: 'Select previous element',
      action: () => {
        // TODO: Implement shift+tab navigation
        console.log('Shift+Tab navigation triggered')
      },
      category: 'Navigation'
    }
  ]

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // Don't trigger shortcuts when typing in inputs
    if (
      event.target instanceof HTMLInputElement ||
      event.target instanceof HTMLTextAreaElement ||
      (event.target as HTMLElement).contentEditable === 'true'
    ) {
      return
    }

    const pressedKeys: string[] = []
    if (event.ctrlKey || event.metaKey) pressedKeys.push('Ctrl')
    if (event.shiftKey) pressedKeys.push('Shift')
    if (event.altKey) pressedKeys.push('Alt')
    
    // Add the main key
    if (event.key === ' ') {
      pressedKeys.push('Space')
    } else if (event.key === 'Escape') {
      pressedKeys.push('Escape')
    } else if (event.key === 'Delete') {
      pressedKeys.push('Delete')
    } else if (event.key === 'Tab') {
      pressedKeys.push('Tab')
    } else if (event.key.length === 1) {
      pressedKeys.push(event.key.toUpperCase())
    }

    // Find matching shortcut
    const matchingShortcut = shortcuts.find(shortcut => {
      if (shortcut.keys.length !== pressedKeys.length) return false
      return shortcut.keys.every(key => pressedKeys.includes(key))
    })

    if (matchingShortcut) {
      event.preventDefault()
      matchingShortcut.action()
    }
  }, [shortcuts, undo, redo, copyToClipboard, pasteFromClipboard, deleteElement, deleteMultipleElements, duplicateElement, selectedElement, selectedElements, setPreviewMode, isPreviewMode, clearSelection, onOpen])

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown)
    return () => {
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [handleKeyDown])

  const groupedShortcuts = shortcuts.reduce((acc, shortcut) => {
    if (!acc[shortcut.category]) {
      acc[shortcut.category] = []
    }
    acc[shortcut.category].push(shortcut)
    return acc
  }, {} as Record<string, KeyboardShortcut[]>)

  const renderShortcut = (shortcut: KeyboardShortcut) => (
    <HStack key={shortcut.description} justify="space-between" py={1}>
      <Text fontSize="sm">{shortcut.description}</Text>
      <HStack spacing={1}>
        {shortcut.keys.map((key, index) => (
          <React.Fragment key={index}>
            <Kbd fontSize="xs">{key}</Kbd>
            {index < shortcut.keys.length - 1 && <Text fontSize="xs">+</Text>}
          </React.Fragment>
        ))}
      </HStack>
    </HStack>
  )

  return (
    <>
      <Modal isOpen={isOpen} onClose={onClose} size="lg">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Keyboard Shortcuts</ModalHeader>
          <ModalCloseButton />
          <ModalBody pb={6}>
            <VStack spacing={4} align="stretch">
              {Object.entries(groupedShortcuts).map(([category, categoryShortcuts]) => (
                <Box key={category}>
                  <Text fontSize="md" fontWeight="semibold" color="blue.600" mb={2}>
                    {category}
                  </Text>
                  <VStack spacing={1} align="stretch">
                    {categoryShortcuts.map(renderShortcut)}
                  </VStack>
                  <Divider mt={3} />
                </Box>
              ))}
              
              <Box mt={4} p={3} bg="gray.50" borderRadius="md" border="1px" borderColor={borderColor}>
                <Text fontSize="sm" color="gray.600">
                  <strong>Tip:</strong> Press <Kbd>?</Kbd> anytime to view these shortcuts. 
                  Most shortcuts work when elements are selected and you're not typing in a text field.
                </Text>
              </Box>
            </VStack>
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  )
}

export default KeyboardShortcuts
