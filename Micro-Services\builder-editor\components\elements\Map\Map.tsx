'use client'

import React, { useState } from 'react'
import {
  Box,
  VStack,
  HStack,
  Text,
  Input,
  Button,
  FormControl,
  FormLabel,
  Switch,
  Slider,
  SliderTrack,
  SliderFilledTrack,
  SliderThumb,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  IconButton,
  useColorModeValue,
  Alert,
  AlertIcon,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel
} from '@chakra-ui/react'
import { EditIcon, SearchIcon } from '@chakra-ui/icons'

export interface MapSettings {
  location: {
    address: string
    lat: number
    lng: number
  }
  zoom: number
  mapType: 'roadmap' | 'satellite' | 'hybrid' | 'terrain'
  style: {
    width: string
    height: string
    borderRadius: number
    border: string
  }
  marker: {
    enabled: boolean
    color: string
    title: string
    description: string
  }
  controls: {
    zoomControl: boolean
    streetViewControl: boolean
    fullscreenControl: boolean
    mapTypeControl: boolean
  }
}

interface MapProps {
  settings: MapSettings
  onChange?: (settings: MapSettings) => void
  isEditing?: boolean
  className?: string
  style?: React.CSSProperties
}

const defaultSettings: MapSettings = {
  location: {
    address: 'New York, NY, USA',
    lat: 40.7128,
    lng: -74.0060
  },
  zoom: 12,
  mapType: 'roadmap',
  style: {
    width: '100%',
    height: '400px',
    borderRadius: 8,
    border: '1px solid #e2e8f0'
  },
  marker: {
    enabled: true,
    color: '#ff0000',
    title: 'Our Location',
    description: 'Visit us here!'
  },
  controls: {
    zoomControl: true,
    streetViewControl: true,
    fullscreenControl: true,
    mapTypeControl: true
  }
}

// Google Maps API key - In production, this should be from environment variables
const GOOGLE_MAPS_API_KEY = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || 'AIzaSyAN4FpvoTeToxAafc_OGlufckos2clD7_k'

const Map: React.FC<MapProps> = ({
  settings = defaultSettings,
  onChange,
  isEditing = false,
  className,
  style
}) => {
  const { isOpen, onOpen, onClose } = useDisclosure()
  const [isSearching, setIsSearching] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')

  // Generate Google Maps Embed URL
  const getMapUrl = () => {
    const { location, zoom, mapType } = settings
    const baseUrl = 'https://www.google.com/maps/embed/v1/place'
    const params = new URLSearchParams({
      key: GOOGLE_MAPS_API_KEY,
      q: `${location.lat},${location.lng}`,
      zoom: zoom.toString(),
      maptype: mapType
    })
    return `${baseUrl}?${params.toString()}`
  }

  // Search for location using Google Geocoding API
  const searchLocation = async () => {
    if (!searchQuery.trim()) return

    setIsSearching(true)
    try {
      const response = await fetch(
        `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(searchQuery)}&key=${GOOGLE_MAPS_API_KEY}`
      )
      const data = await response.json()
      
      if (data.results && data.results.length > 0) {
        const result = data.results[0]
        const newLocation = {
          address: result.formatted_address,
          lat: result.geometry.location.lat,
          lng: result.geometry.location.lng
        }
        
        if (onChange) {
          onChange({
            ...settings,
            location: newLocation
          })
        }
      }
    } catch (error) {
      console.error('Error searching location:', error)
    } finally {
      setIsSearching(false)
    }
  }

  if (isEditing) {
    return (
      <Box className={className} style={style}>
        <Button onClick={onOpen} size="sm" colorScheme="blue">
          Edit Map
        </Button>
        
        <Modal isOpen={isOpen} onClose={onClose} size="xl">
          <ModalOverlay />
          <ModalContent>
            <ModalHeader>Map Settings</ModalHeader>
            <ModalCloseButton />
            <ModalBody pb={6}>
              <MapSettings
                settings={settings}
                onChange={onChange || (() => {})}
                searchQuery={searchQuery}
                setSearchQuery={setSearchQuery}
                searchLocation={searchLocation}
                isSearching={isSearching}
              />
            </ModalBody>
          </ModalContent>
        </Modal>

        {/* Preview */}
        <Box mt={4} p={4} border="1px dashed" borderColor="gray.300" borderRadius="md">
          <Text fontSize="sm" color="gray.500" mb={2}>Preview:</Text>
          <Box
            width={settings.style.width}
            height={settings.style.height}
            borderRadius={settings.style.borderRadius}
            border={settings.style.border}
            overflow="hidden"
          >
            <iframe
              src={getMapUrl()}
              width="100%"
              height="100%"
              style={{ border: 0 }}
              allowFullScreen
              loading="lazy"
              referrerPolicy="no-referrer-when-downgrade"
            />
          </Box>
        </Box>
      </Box>
    )
  }

  return (
    <Box className={className} style={style}>
      <Box
        width={settings.style.width}
        height={settings.style.height}
        borderRadius={settings.style.borderRadius}
        border={settings.style.border}
        overflow="hidden"
        position="relative"
      >
        <iframe
          src={getMapUrl()}
          width="100%"
          height="100%"
          style={{ border: 0 }}
          allowFullScreen
          loading="lazy"
          referrerPolicy="no-referrer-when-downgrade"
        />
        
        {isEditing && (
          <IconButton
            aria-label="Edit map"
            icon={<EditIcon />}
            size="sm"
            position="absolute"
            top={2}
            right={2}
            zIndex={2}
            onClick={onOpen}
            colorScheme="blue"
          />
        )}
      </Box>
    </Box>
  )
}

// Settings Component
const MapSettings: React.FC<{
  settings: MapSettings
  onChange: (settings: MapSettings) => void
  searchQuery: string
  setSearchQuery: (query: string) => void
  searchLocation: () => void
  isSearching: boolean
}> = ({ settings, onChange, searchQuery, setSearchQuery, searchLocation, isSearching }) => {
  const borderColor = useColorModeValue('gray.200', 'gray.600')

  return (
    <VStack spacing={6} align="stretch">
      <Tabs>
        <TabList>
          <Tab>Location</Tab>
          <Tab>Appearance</Tab>
          <Tab>Controls</Tab>
        </TabList>

        <TabPanels>
          {/* Location Tab */}
          <TabPanel>
            <VStack spacing={4} align="stretch">
              {/* Location Search */}
              <Box>
                <FormLabel fontSize="sm">Search Location</FormLabel>
                <HStack>
                  <Input
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Enter address or place name"
                    size="sm"
                    onKeyPress={(e) => e.key === 'Enter' && searchLocation()}
                  />
                  <Button
                    leftIcon={<SearchIcon />}
                    onClick={searchLocation}
                    isLoading={isSearching}
                    size="sm"
                    colorScheme="blue"
                  >
                    Search
                  </Button>
                </HStack>
              </Box>

              {/* Current Location */}
              <Box>
                <FormLabel fontSize="sm">Current Location</FormLabel>
                <Text fontSize="sm" color="gray.600" mb={2}>
                  {settings.location.address}
                </Text>
                <HStack spacing={3}>
                  <FormControl>
                    <FormLabel fontSize="xs">Latitude</FormLabel>
                    <Input
                      value={settings.location.lat}
                      onChange={(e) => onChange({
                        ...settings,
                        location: {
                          ...settings.location,
                          lat: parseFloat(e.target.value) || 0
                        }
                      })}
                      size="sm"
                      type="number"
                      step="any"
                    />
                  </FormControl>
                  <FormControl>
                    <FormLabel fontSize="xs">Longitude</FormLabel>
                    <Input
                      value={settings.location.lng}
                      onChange={(e) => onChange({
                        ...settings,
                        location: {
                          ...settings.location,
                          lng: parseFloat(e.target.value) || 0
                        }
                      })}
                      size="sm"
                      type="number"
                      step="any"
                    />
                  </FormControl>
                </HStack>
              </Box>

              {/* Zoom Level */}
              <FormControl>
                <FormLabel fontSize="sm">Zoom Level: {settings.zoom}</FormLabel>
                <Slider
                  value={settings.zoom}
                  onChange={(value) => onChange({
                    ...settings,
                    zoom: value
                  })}
                  min={1}
                  max={20}
                  step={1}
                >
                  <SliderTrack>
                    <SliderFilledTrack />
                  </SliderTrack>
                  <SliderThumb />
                </Slider>
              </FormControl>

              {/* Map Type */}
              <FormControl>
                <FormLabel fontSize="sm">Map Type</FormLabel>
                <HStack spacing={2}>
                  {['roadmap', 'satellite', 'hybrid', 'terrain'].map((type) => (
                    <Button
                      key={type}
                      size="sm"
                      variant={settings.mapType === type ? 'solid' : 'outline'}
                      colorScheme="blue"
                      onClick={() => onChange({
                        ...settings,
                        mapType: type as any
                      })}
                    >
                      {type.charAt(0).toUpperCase() + type.slice(1)}
                    </Button>
                  ))}
                </HStack>
              </FormControl>
            </VStack>
          </TabPanel>

          {/* Appearance Tab */}
          <TabPanel>
            <VStack spacing={4} align="stretch">
              <HStack spacing={3}>
                <FormControl>
                  <FormLabel fontSize="sm">Width</FormLabel>
                  <Input
                    value={settings.style.width}
                    onChange={(e) => onChange({
                      ...settings,
                      style: { ...settings.style, width: e.target.value }
                    })}
                    size="sm"
                    placeholder="100%"
                  />
                </FormControl>
                <FormControl>
                  <FormLabel fontSize="sm">Height</FormLabel>
                  <Input
                    value={settings.style.height}
                    onChange={(e) => onChange({
                      ...settings,
                      style: { ...settings.style, height: e.target.value }
                    })}
                    size="sm"
                    placeholder="400px"
                  />
                </FormControl>
              </HStack>

              <FormControl>
                <FormLabel fontSize="sm">Border Radius: {settings.style.borderRadius}px</FormLabel>
                <Slider
                  value={settings.style.borderRadius}
                  onChange={(value) => onChange({
                    ...settings,
                    style: { ...settings.style, borderRadius: value }
                  })}
                  min={0}
                  max={50}
                  step={1}
                >
                  <SliderTrack>
                    <SliderFilledTrack />
                  </SliderTrack>
                  <SliderThumb />
                </Slider>
              </FormControl>

              <FormControl>
                <FormLabel fontSize="sm">Border</FormLabel>
                <Input
                  value={settings.style.border}
                  onChange={(e) => onChange({
                    ...settings,
                    style: { ...settings.style, border: e.target.value }
                  })}
                  size="sm"
                  placeholder="1px solid #e2e8f0"
                />
              </FormControl>
            </VStack>
          </TabPanel>

          {/* Controls Tab */}
          <TabPanel>
            <VStack spacing={4} align="stretch">
              <Text fontSize="sm" fontWeight="semibold">Map Controls</Text>
              
              {Object.entries(settings.controls).map(([key, value]) => (
                <FormControl key={key} display="flex" alignItems="center">
                  <FormLabel fontSize="sm" mb="0" flex="1">
                    {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                  </FormLabel>
                  <Switch
                    isChecked={value}
                    onChange={(e) => onChange({
                      ...settings,
                      controls: {
                        ...settings.controls,
                        [key]: e.target.checked
                      }
                    })}
                  />
                </FormControl>
              ))}

              <Alert status="info" size="sm">
                <AlertIcon />
                <Text fontSize="xs">
                  Note: Some controls may not be visible in embed mode depending on Google Maps settings.
                </Text>
              </Alert>
            </VStack>
          </TabPanel>
        </TabPanels>
      </Tabs>
    </VStack>
  )
}

export default Map
