'use client'

import { useState } from 'react'
import {
  Box,
  Button,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  Modal<PERSON>ooter,
  ModalBody,
  ModalCloseButton,
  VStack,
  HStack,
  Text,
  Input,
  IconButton,
  useDisclosure,
  useToast,
  Divider,
  Badge,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  useColorModeValue
} from '@chakra-ui/react'
import {
  AddIcon,
  CopyIcon,
  DeleteIcon,
  EditIcon,
  ChevronDownIcon,
  ExternalLinkIcon
} from '@chakra-ui/icons'
import { useEditorStore } from '@/lib/stores/editorStore'

interface Page {
  id: string
  name: string
  slug: string
  sections: any[]
  seoSettings?: any
  settings?: any
  isPublished?: boolean
  lastModified?: string
}

export function PageManager() {
  const { isOpen, onOpen, onClose } = useDisclosure()
  const [pages, setPages] = useState<Page[]>([
    {
      id: 'demo-page-1',
      name: 'Demo Landing Page',
      slug: 'demo-landing',
      sections: [],
      isPublished: false,
      lastModified: new Date().toISOString()
    }
  ])
  const [newPageName, setNewPageName] = useState('')
  const [isCreating, setIsCreating] = useState(false)
  const toast = useToast()
  
  const { currentPage, setCurrentPage } = useEditorStore()
  
  const bgColor = useColorModeValue('white', 'gray.800')
  const borderColor = useColorModeValue('gray.200', 'gray.600')

  const handleCreatePage = async () => {
    if (!newPageName.trim()) return
    
    setIsCreating(true)
    
    try {
      const newPage: Page = {
        id: `page-${Date.now()}`,
        name: newPageName.trim(),
        slug: newPageName.toLowerCase().replace(/\s+/g, '-'),
        sections: [],
        isPublished: false,
        lastModified: new Date().toISOString()
      }
      
      setPages(prev => [...prev, newPage])
      setNewPageName('')
      onClose()
      
      toast({
        title: 'Page created',
        description: `"${newPage.name}" has been created successfully`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      })
      
      // Switch to the new page
      setCurrentPage(newPage)
      
    } catch (error) {
      toast({
        title: 'Failed to create page',
        description: 'An error occurred while creating the page',
        status: 'error',
        duration: 5000,
        isClosable: true,
      })
    } finally {
      setIsCreating(false)
    }
  }

  const handleDuplicatePage = async (page: Page) => {
    try {
      const duplicatedPage: Page = {
        ...page,
        id: `page-${Date.now()}`,
        name: `${page.name} (Copy)`,
        slug: `${page.slug}-copy`,
        isPublished: false,
        lastModified: new Date().toISOString()
      }
      
      setPages(prev => [...prev, duplicatedPage])
      
      toast({
        title: 'Page duplicated',
        description: `"${duplicatedPage.name}" has been created`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      })
      
    } catch (error) {
      toast({
        title: 'Failed to duplicate page',
        description: 'An error occurred while duplicating the page',
        status: 'error',
        duration: 5000,
        isClosable: true,
      })
    }
  }

  const handleDeletePage = async (pageId: string) => {
    if (pages.length <= 1) {
      toast({
        title: 'Cannot delete page',
        description: 'You must have at least one page',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      })
      return
    }
    
    try {
      setPages(prev => prev.filter(p => p.id !== pageId))
      
      // If deleted page was current, switch to first available page
      if (currentPage?.id === pageId) {
        const remainingPages = pages.filter(p => p.id !== pageId)
        if (remainingPages.length > 0) {
          setCurrentPage(remainingPages[0])
        }
      }
      
      toast({
        title: 'Page deleted',
        description: 'The page has been deleted successfully',
        status: 'success',
        duration: 3000,
        isClosable: true,
      })
      
    } catch (error) {
      toast({
        title: 'Failed to delete page',
        description: 'An error occurred while deleting the page',
        status: 'error',
        duration: 5000,
        isClosable: true,
      })
    }
  }

  const handleSelectPage = (page: Page) => {
    setCurrentPage(page)
  }

  return (
    <>
      <Menu>
        <MenuButton
          as={Button}
          rightIcon={<ChevronDownIcon />}
          size="sm"
          variant="outline"
        >
          {currentPage?.name || 'Select Page'}
        </MenuButton>
        <MenuList>
          <MenuItem onClick={onOpen} icon={<AddIcon />}>
            Add New Page
          </MenuItem>
          <Divider />
          {pages.map((page) => (
            <MenuItem
              key={page.id}
              onClick={() => handleSelectPage(page)}
              bg={currentPage?.id === page.id ? 'blue.50' : 'transparent'}
            >
              <HStack justify="space-between" w="100%">
                <VStack align="start" spacing={0}>
                  <Text fontSize="sm" fontWeight="medium">
                    {page.name}
                  </Text>
                  <HStack spacing={2}>
                    <Badge
                      size="sm"
                      colorScheme={page.isPublished ? 'green' : 'gray'}
                    >
                      {page.isPublished ? 'Published' : 'Draft'}
                    </Badge>
                    <Text fontSize="xs" color="gray.500">
                      {page.sections.length} sections
                    </Text>
                  </HStack>
                </VStack>
                <HStack spacing={1}>
                  <IconButton
                    aria-label="Duplicate page"
                    icon={<CopyIcon />}
                    size="xs"
                    variant="ghost"
                    onClick={(e) => {
                      e.stopPropagation()
                      handleDuplicatePage(page)
                    }}
                  />
                  <IconButton
                    aria-label="Delete page"
                    icon={<DeleteIcon />}
                    size="xs"
                    variant="ghost"
                    colorScheme="red"
                    onClick={(e) => {
                      e.stopPropagation()
                      handleDeletePage(page.id)
                    }}
                  />
                </HStack>
              </HStack>
            </MenuItem>
          ))}
        </MenuList>
      </Menu>

      {/* Create Page Modal */}
      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Create New Page</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <VStack spacing={4} align="stretch">
              <Box>
                <Text fontSize="sm" fontWeight="medium" mb={2}>
                  Page Name
                </Text>
                <Input
                  placeholder="Enter page name"
                  value={newPageName}
                  onChange={(e) => setNewPageName(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      handleCreatePage()
                    }
                  }}
                />
              </Box>
              {newPageName && (
                <Box>
                  <Text fontSize="sm" color="gray.500">
                    URL: /{newPageName.toLowerCase().replace(/\s+/g, '-')}
                  </Text>
                </Box>
              )}
            </VStack>
          </ModalBody>
          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={onClose}>
              Cancel
            </Button>
            <Button
              colorScheme="blue"
              onClick={handleCreatePage}
              isLoading={isCreating}
              isDisabled={!newPageName.trim()}
            >
              Create Page
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  )
}
