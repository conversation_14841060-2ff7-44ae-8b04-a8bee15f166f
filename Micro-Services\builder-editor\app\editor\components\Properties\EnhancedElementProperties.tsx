'use client'

import React, { useState } from 'react'
import {
  Box,
  VStack,
  HStack,
  Text,
  Input,
  Textarea,
  Select,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  FormControl,
  FormLabel,
  Switch,
  Button,
  ButtonGroup,
  Divider,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  useColorModeValue,
  Badge,
  IconButton,
  Tooltip,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Slider,
  SliderTrack,
  SliderFilledTrack,
  SliderThumb,
  Alert,
  AlertIcon
} from '@chakra-ui/react'
import { 
  CopyIcon, 
  DeleteIcon, 
  RepeatIcon,
  ViewIcon,
  EditIcon,
  SettingsIcon,
  ChevronUpIcon,
  ChevronDownIcon
} from '@chakra-ui/icons'
import { Element, useEditorStore } from '@/lib/stores/editorStore'

interface EnhancedElementPropertiesProps {
  element?: Element
  elements?: Element[]
}

export function EnhancedElementProperties({ element, elements }: EnhancedElementPropertiesProps) {
  const [activeTab, setActiveTab] = useState(0)
  const borderColor = useColorModeValue('gray.200', 'gray.700')
  const bgColor = useColorModeValue('white', 'gray.800')
  
  const {
    updateElement,
    updateMultipleElements,
    deleteElement,
    deleteMultipleElements,
    duplicateElement,
    copyToClipboard,
    selectedElements,
    currentBreakpoint
  } = useEditorStore()

  const isMultiSelect = elements && elements.length > 1
  const targetElement = element || (elements && elements[0])
  const targetElements = elements || (element ? [element] : [])

  if (!targetElement) {
    return (
      <Box p={4} textAlign="center">
        <Text color="gray.500" fontSize="sm">
          Select an element to edit properties
        </Text>
      </Box>
    )
  }

  const handleSinglePropChange = (key: string, value: any) => {
    if (targetElement) {
      updateElement(targetElement.id, {
        props: {
          ...targetElement.props,
          [key]: value
        }
      })
    }
  }

  const handleMultiplePropChange = (key: string, value: any) => {
    if (isMultiSelect && targetElements.length > 0) {
      const updates = targetElements.map(el => ({
        id: el.id,
        props: {
          ...el.props,
          [key]: value
        }
      }))
      updateMultipleElements(updates)
    } else {
      handleSinglePropChange(key, value)
    }
  }

  const handleSingleStyleChange = (key: string, value: any) => {
    if (targetElement) {
      updateElement(targetElement.id, {
        style: {
          ...targetElement.style,
          [key]: value
        }
      })
    }
  }

  const handleMultipleStyleChange = (key: string, value: any) => {
    if (isMultiSelect && targetElements.length > 0) {
      const updates = targetElements.map(el => ({
        id: el.id,
        style: {
          ...el.style,
          [key]: value
        }
      }))
      updateMultipleElements(updates)
    } else {
      handleSingleStyleChange(key, value)
    }
  }

  const handleDelete = () => {
    if (isMultiSelect) {
      deleteMultipleElements(targetElements.map(el => el.id))
    } else if (targetElement) {
      deleteElement(targetElement.id)
    }
  }

  const handleDuplicate = () => {
    if (targetElement) {
      duplicateElement(targetElement.id)
    }
  }

  const handleCopy = () => {
    copyToClipboard(targetElements)
  }

  const getCommonValue = (key: string, source: 'props' | 'style') => {
    if (!isMultiSelect || targetElements.length === 0) {
      return source === 'props' ? targetElement?.props[key] : targetElement?.style[key]
    }

    const values = targetElements.map(el => 
      source === 'props' ? el.props[key] : el.style[key]
    )
    
    const firstValue = values[0]
    const allSame = values.every(val => val === firstValue)
    
    return allSame ? firstValue : undefined
  }

  const renderElementHeader = () => (
    <Box>
      <HStack justify="space-between" mb={2}>
        <VStack align="start" spacing={0}>
          <HStack>
            <Text fontSize="sm" fontWeight="semibold" color="gray.700">
              {isMultiSelect ? 'Multiple Elements' : `${targetElement.type.charAt(0).toUpperCase() + targetElement.type.slice(1)} Element`}
            </Text>
            {isMultiSelect && (
              <Badge colorScheme="blue" size="sm">
                {targetElements.length} selected
              </Badge>
            )}
          </HStack>
          <Text fontSize="xs" color="gray.500">
            {isMultiSelect ? 'Bulk editing mode' : `ID: ${targetElement.id}`}
          </Text>
        </VStack>
        
        <HStack spacing={1}>
          <Tooltip label="Copy">
            <IconButton
              aria-label="Copy"
              icon={<CopyIcon />}
              size="xs"
              variant="ghost"
              onClick={handleCopy}
            />
          </Tooltip>
          {!isMultiSelect && (
            <Tooltip label="Duplicate">
              <IconButton
                aria-label="Duplicate"
                icon={<RepeatIcon />}
                size="xs"
                variant="ghost"
                onClick={handleDuplicate}
              />
            </Tooltip>
          )}
          <Tooltip label="Delete">
            <IconButton
              aria-label="Delete"
              icon={<DeleteIcon />}
              size="xs"
              variant="ghost"
              colorScheme="red"
              onClick={handleDelete}
            />
          </Tooltip>
        </HStack>
      </HStack>
      
      {isMultiSelect && (
        <Alert status="info" size="sm" mb={3}>
          <AlertIcon />
          <Text fontSize="xs">
            Changes will be applied to all {targetElements.length} selected elements
          </Text>
        </Alert>
      )}
    </Box>
  )

  const renderContentTab = () => {
    const elementType = targetElement.type
    
    switch (elementType) {
      case 'text':
        return (
          <VStack spacing={3} align="stretch">
            <FormControl>
              <FormLabel fontSize="sm">Content</FormLabel>
              <Textarea
                value={getCommonValue('content', 'props') || ''}
                onChange={(e) => handleMultiplePropChange('content', e.target.value)}
                size="sm"
                rows={3}
                placeholder={isMultiSelect ? 'Mixed values' : 'Enter text content'}
              />
            </FormControl>
            
            <HStack>
              <FormControl>
                <FormLabel fontSize="sm">Font Size</FormLabel>
                <Input
                  value={getCommonValue('fontSize', 'props') || ''}
                  onChange={(e) => handleMultiplePropChange('fontSize', e.target.value)}
                  size="sm"
                  placeholder="16px"
                />
              </FormControl>
              
              <FormControl>
                <FormLabel fontSize="sm">Font Weight</FormLabel>
                <Select
                  value={getCommonValue('fontWeight', 'props') || 'normal'}
                  onChange={(e) => handleMultiplePropChange('fontWeight', e.target.value)}
                  size="sm"
                >
                  <option value="normal">Normal</option>
                  <option value="bold">Bold</option>
                  <option value="lighter">Light</option>
                  <option value="600">Semi Bold</option>
                </Select>
              </FormControl>
            </HStack>
            
            <FormControl>
              <FormLabel fontSize="sm">Text Align</FormLabel>
              <ButtonGroup size="sm" isAttached>
                {['left', 'center', 'right', 'justify'].map(align => (
                  <Button
                    key={align}
                    variant={getCommonValue('textAlign', 'props') === align ? 'solid' : 'outline'}
                    onClick={() => handleMultiplePropChange('textAlign', align)}
                    size="sm"
                  >
                    {align.charAt(0).toUpperCase()}
                  </Button>
                ))}
              </ButtonGroup>
            </FormControl>
          </VStack>
        )
        
      case 'image':
        return (
          <VStack spacing={3} align="stretch">
            <FormControl>
              <FormLabel fontSize="sm">Image URL</FormLabel>
              <Input
                value={getCommonValue('src', 'props') || ''}
                onChange={(e) => handleMultiplePropChange('src', e.target.value)}
                size="sm"
                placeholder="https://example.com/image.jpg"
              />
            </FormControl>
            
            <FormControl>
              <FormLabel fontSize="sm">Alt Text</FormLabel>
              <Input
                value={getCommonValue('alt', 'props') || ''}
                onChange={(e) => handleMultiplePropChange('alt', e.target.value)}
                size="sm"
                placeholder="Image description"
              />
            </FormControl>
            
            <HStack>
              <FormControl>
                <FormLabel fontSize="sm">Width</FormLabel>
                <Input
                  value={getCommonValue('width', 'style') || ''}
                  onChange={(e) => handleMultipleStyleChange('width', e.target.value)}
                  size="sm"
                  placeholder="auto"
                />
              </FormControl>
              
              <FormControl>
                <FormLabel fontSize="sm">Height</FormLabel>
                <Input
                  value={getCommonValue('height', 'style') || ''}
                  onChange={(e) => handleMultipleStyleChange('height', e.target.value)}
                  size="sm"
                  placeholder="auto"
                />
              </FormControl>
            </HStack>
          </VStack>
        )
        
      case 'button':
        return (
          <VStack spacing={3} align="stretch">
            <FormControl>
              <FormLabel fontSize="sm">Button Text</FormLabel>
              <Input
                value={getCommonValue('text', 'props') || ''}
                onChange={(e) => handleMultiplePropChange('text', e.target.value)}
                size="sm"
                placeholder="Button text"
              />
            </FormControl>
            
            <HStack>
              <FormControl>
                <FormLabel fontSize="sm">Color Scheme</FormLabel>
                <Select
                  value={getCommonValue('colorScheme', 'props') || 'blue'}
                  onChange={(e) => handleMultiplePropChange('colorScheme', e.target.value)}
                  size="sm"
                >
                  <option value="blue">Blue</option>
                  <option value="green">Green</option>
                  <option value="red">Red</option>
                  <option value="orange">Orange</option>
                  <option value="purple">Purple</option>
                </Select>
              </FormControl>
              
              <FormControl>
                <FormLabel fontSize="sm">Size</FormLabel>
                <Select
                  value={getCommonValue('size', 'props') || 'md'}
                  onChange={(e) => handleMultiplePropChange('size', e.target.value)}
                  size="sm"
                >
                  <option value="xs">Extra Small</option>
                  <option value="sm">Small</option>
                  <option value="md">Medium</option>
                  <option value="lg">Large</option>
                </Select>
              </FormControl>
            </HStack>
            
            <FormControl>
              <FormLabel fontSize="sm">Link URL</FormLabel>
              <Input
                value={getCommonValue('href', 'props') || ''}
                onChange={(e) => handleMultiplePropChange('href', e.target.value)}
                size="sm"
                placeholder="https://example.com"
              />
            </FormControl>
          </VStack>
        )
        
      default:
        return (
          <Text fontSize="sm" color="gray.500">
            {isMultiSelect ? 'Mixed element types selected' : `No specific properties for ${elementType} elements`}
          </Text>
        )
    }
  }

  return (
    <Box p={4} bg={bgColor} borderRadius="md">
      <VStack spacing={4} align="stretch">
        {renderElementHeader()}
        
        <Divider />
        
        <Tabs index={activeTab} onChange={setActiveTab} size="sm">
          <TabList>
            <Tab>Content</Tab>
            <Tab>Style</Tab>
            <Tab>Layout</Tab>
            <Tab>Advanced</Tab>
          </TabList>
          
          <TabPanels>
            <TabPanel px={0}>
              {renderContentTab()}
            </TabPanel>
            
            <TabPanel px={0}>
              <VStack spacing={3} align="stretch">
                <FormControl>
                  <FormLabel fontSize="sm">Background Color</FormLabel>
                  <Input
                    type="color"
                    value={getCommonValue('backgroundColor', 'style') || '#ffffff'}
                    onChange={(e) => handleMultipleStyleChange('backgroundColor', e.target.value)}
                    size="sm"
                  />
                </FormControl>
                
                <FormControl>
                  <FormLabel fontSize="sm">Text Color</FormLabel>
                  <Input
                    type="color"
                    value={getCommonValue('color', 'style') || '#000000'}
                    onChange={(e) => handleMultipleStyleChange('color', e.target.value)}
                    size="sm"
                  />
                </FormControl>
                
                <FormControl>
                  <FormLabel fontSize="sm">Border Radius</FormLabel>
                  <Input
                    value={getCommonValue('borderRadius', 'style') || ''}
                    onChange={(e) => handleMultipleStyleChange('borderRadius', e.target.value)}
                    size="sm"
                    placeholder="0px"
                  />
                </FormControl>
                
                <FormControl>
                  <FormLabel fontSize="sm">Opacity: {Math.round((getCommonValue('opacity', 'style') || 1) * 100)}%</FormLabel>
                  <Slider
                    value={(getCommonValue('opacity', 'style') || 1) * 100}
                    onChange={(val) => handleMultipleStyleChange('opacity', val / 100)}
                    min={0}
                    max={100}
                  >
                    <SliderTrack>
                      <SliderFilledTrack />
                    </SliderTrack>
                    <SliderThumb />
                  </Slider>
                </FormControl>
              </VStack>
            </TabPanel>
            
            <TabPanel px={0}>
              <VStack spacing={3} align="stretch">
                <HStack>
                  <FormControl>
                    <FormLabel fontSize="sm">Margin</FormLabel>
                    <Input
                      value={getCommonValue('margin', 'style') || ''}
                      onChange={(e) => handleMultipleStyleChange('margin', e.target.value)}
                      size="sm"
                      placeholder="0px"
                    />
                  </FormControl>
                  
                  <FormControl>
                    <FormLabel fontSize="sm">Padding</FormLabel>
                    <Input
                      value={getCommonValue('padding', 'style') || ''}
                      onChange={(e) => handleMultipleStyleChange('padding', e.target.value)}
                      size="sm"
                      placeholder="0px"
                    />
                  </FormControl>
                </HStack>
                
                <FormControl>
                  <FormLabel fontSize="sm">Display</FormLabel>
                  <Select
                    value={getCommonValue('display', 'style') || 'block'}
                    onChange={(e) => handleMultipleStyleChange('display', e.target.value)}
                    size="sm"
                  >
                    <option value="block">Block</option>
                    <option value="inline">Inline</option>
                    <option value="inline-block">Inline Block</option>
                    <option value="flex">Flex</option>
                    <option value="grid">Grid</option>
                    <option value="none">None</option>
                  </Select>
                </FormControl>
                
                <FormControl>
                  <FormLabel fontSize="sm">Position</FormLabel>
                  <Select
                    value={getCommonValue('position', 'style') || 'static'}
                    onChange={(e) => handleMultipleStyleChange('position', e.target.value)}
                    size="sm"
                  >
                    <option value="static">Static</option>
                    <option value="relative">Relative</option>
                    <option value="absolute">Absolute</option>
                    <option value="fixed">Fixed</option>
                    <option value="sticky">Sticky</option>
                  </Select>
                </FormControl>
              </VStack>
            </TabPanel>
            
            <TabPanel px={0}>
              <VStack spacing={3} align="stretch">
                <FormControl>
                  <FormLabel fontSize="sm">Z-Index</FormLabel>
                  <NumberInput
                    value={getCommonValue('zIndex', 'style') || 0}
                    onChange={(_, val) => handleMultipleStyleChange('zIndex', val)}
                    size="sm"
                  >
                    <NumberInputField />
                    <NumberInputStepper>
                      <NumberIncrementStepper />
                      <NumberDecrementStepper />
                    </NumberInputStepper>
                  </NumberInput>
                </FormControl>
                
                <FormControl>
                  <FormLabel fontSize="sm">Transform</FormLabel>
                  <Input
                    value={getCommonValue('transform', 'style') || ''}
                    onChange={(e) => handleMultipleStyleChange('transform', e.target.value)}
                    size="sm"
                    placeholder="rotate(0deg) scale(1)"
                  />
                </FormControl>
                
                <FormControl>
                  <FormLabel fontSize="sm">Transition</FormLabel>
                  <Input
                    value={getCommonValue('transition', 'style') || ''}
                    onChange={(e) => handleMultipleStyleChange('transition', e.target.value)}
                    size="sm"
                    placeholder="all 0.3s ease"
                  />
                </FormControl>
                
                <FormControl>
                  <FormLabel fontSize="sm">Custom CSS</FormLabel>
                  <Textarea
                    value={getCommonValue('customCSS', 'style') || ''}
                    onChange={(e) => handleMultipleStyleChange('customCSS', e.target.value)}
                    size="sm"
                    rows={3}
                    placeholder="Custom CSS properties"
                  />
                </FormControl>
              </VStack>
            </TabPanel>
          </TabPanels>
        </Tabs>
      </VStack>
    </Box>
  )
}

export default EnhancedElementProperties
