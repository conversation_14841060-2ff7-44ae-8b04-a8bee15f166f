'use client'

import { useState } from 'react'
import { Box, Flex, useColorModeValue } from '@chakra-ui/react'
import { EditorTopBar } from './EditorTopBar'
import { EditorSidebar } from './EditorSidebar'
import { EditorCanvas } from './EditorCanvas'
import { EditorRightPanel } from './EditorRightPanel'
import { useEditorStore } from '@/lib/stores/editorStore'

// Import global components
import KeyboardShortcuts from '../KeyboardShortcuts/KeyboardShortcuts'
import AutoSave from '../AutoSave/AutoSave'

export function EditorLayout() {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [rightPanelCollapsed, setRightPanelCollapsed] = useState(false)
  
  const bgColor = useColorModeValue('#f7fafc', '#1a202c')
  const { isPreviewMode } = useEditorStore()

  // Editor layout style matching Old Builder
  const editorStyle: React.CSSProperties = {
    height: '100vh',
    position: 'absolute',
    userSelect: 'none',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#283340',
    display: 'flex',
    flexDirection: 'column',
    overflow: 'hidden',
  }

  return (
    <Box style={editorStyle}>
      {/* Top Bar - matches Old Builder's TopBar */}
      <EditorTopBar />
      
      {/* Main Editor Area */}
      <Flex height="calc(100vh - 60px)" overflow="hidden">
        {/* Left Sidebar - matches Old Builder's SideBar */}
        {!isPreviewMode && (
          <EditorSidebar 
            collapsed={sidebarCollapsed}
            onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
          />
        )}
        
        {/* Main Canvas Area - matches Old Builder's EditorIframe */}
        <EditorCanvas />
        
        {/* Right Panel - Properties and Layers */}
        {!isPreviewMode && (
          <EditorRightPanel 
            collapsed={rightPanelCollapsed}
            onToggleCollapse={() => setRightPanelCollapsed(!rightPanelCollapsed)}
          />
        )}
      </Flex>

      {/* Global Components */}
      <KeyboardShortcuts />
      <AutoSave enabled={false} interval={30000} />
      
      {/* Portal for modals and overlays */}
      <div id="editor-portal" />
    </Box>
  )
}
