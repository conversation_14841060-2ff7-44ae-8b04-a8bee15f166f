'use client'

import {
  Box,
  VStack,
  Text,
  Image,
  useColorModeValue,
  Tooltip
} from '@chakra-ui/react'
import { motion } from 'framer-motion'

interface SectionTemplate {
  id: string
  type: 'hero' | 'feature' | 'testimonial' | 'contact' | 'gallery' | 'custom'
  name: string
  description: string
  thumbnail: string
  defaultStyle: Record<string, any>
  defaultElements: any[]
}

// Section templates based on Old Builder analysis
const sectionTemplates: SectionTemplate[] = [
  {
    id: 'hero-1',
    type: 'hero',
    name: 'Hero Banner',
    description: 'Large header section with title, subtitle and CTA',
    thumbnail: '/api/placeholder-image?width=300&height=200&text=Hero%20Banner&bg=1a202c&color=ffffff',
    defaultStyle: {
      padding: '100px 20px',
      backgroundColor: '#1a202c',
      color: 'white',
      textAlign: 'center'
    },
    defaultElements: [
      {
        id: 'hero-title',
        type: 'text',
        props: {
          content: 'Welcome to Our Amazing Product',
          fontSize: '48px',
          fontWeight: 'bold',
          marginBottom: '20px'
        },
        style: {}
      },
      {
        id: 'hero-subtitle',
        type: 'text',
        props: {
          content: 'Build something amazing with our powerful tools',
          fontSize: '20px',
          marginBottom: '40px',
          opacity: 0.9
        },
        style: {}
      },
      {
        id: 'hero-cta',
        type: 'button',
        props: {
          text: 'Get Started',
          colorScheme: 'blue',
          size: 'lg'
        },
        style: {}
      }
    ]
  },
  {
    id: 'feature-1',
    type: 'feature',
    name: 'Feature Grid',
    description: '3-column feature showcase',
    thumbnail: '/api/placeholder-image?width=300&height=200&text=Feature%20Grid&bg=ffffff&color=333333',
    defaultStyle: {
      padding: '80px 20px',
      backgroundColor: '#ffffff'
    },
    defaultElements: [
      {
        id: 'feature-title',
        type: 'text',
        props: {
          content: 'Our Features',
          fontSize: '36px',
          fontWeight: 'bold',
          textAlign: 'center',
          marginBottom: '60px'
        },
        style: {}
      }
    ]
  },
  {
    id: 'testimonial-1',
    type: 'testimonial',
    name: 'Customer Reviews',
    description: 'Testimonial cards with customer feedback',
    thumbnail: '/api/placeholder-image?width=300&height=200&text=Testimonials&bg=f7fafc&color=2d3748',
    defaultStyle: {
      padding: '80px 20px',
      backgroundColor: '#f7fafc'
    },
    defaultElements: []
  },
  {
    id: 'contact-1',
    type: 'contact',
    name: 'Contact Form',
    description: 'Contact section with form and info',
    thumbnail: '/api/placeholder-image?width=300&height=200&text=Contact%20Form&bg=ffffff&color=333333',
    defaultStyle: {
      padding: '80px 20px',
      backgroundColor: '#ffffff'
    },
    defaultElements: [
      {
        id: 'contact-form',
        type: 'form',
        props: {
          formType: 'contact',
          fields: ['name', 'email', 'message']
        },
        style: {}
      }
    ]
  },
  {
    id: 'gallery-1',
    type: 'gallery',
    name: 'Image Gallery',
    description: 'Responsive image gallery grid',
    thumbnail: '/api/placeholder-image?width=300&height=200&text=Image%20Gallery&bg=ffffff&color=333333',
    defaultStyle: {
      padding: '80px 20px',
      backgroundColor: '#ffffff'
    },
    defaultElements: []
  },
  {
    id: 'custom-1',
    type: 'custom',
    name: 'Blank Section',
    description: 'Empty section to build from scratch',
    thumbnail: '/api/placeholder-image?width=300&height=200&text=Blank%20Section&bg=f8f9fa&color=6c757d',
    defaultStyle: {
      padding: '60px 20px',
      backgroundColor: '#ffffff',
      minHeight: '200px'
    },
    defaultElements: []
  }
]

export function SectionPalette() {
  const borderColor = useColorModeValue('gray.200', 'gray.700')
  const hoverBg = useColorModeValue('gray.50', 'gray.700')

  const handleDragStart = (e: React.DragEvent, template: SectionTemplate) => {
    e.dataTransfer.setData('application/json', JSON.stringify({
      type: 'section',
      sectionType: template.type,
      name: template.name,
      defaultStyle: template.defaultStyle,
      defaultElements: template.defaultElements
    }))
  }

  return (
    <VStack spacing={0} align="stretch" p={3}>
      <Text fontSize="sm" fontWeight="semibold" color="gray.600" mb={3}>
        Drag sections to canvas
      </Text>
      
      {sectionTemplates.map((template) => (
        <Tooltip
          key={template.id}
          label={template.description}
          placement="right"
          hasArrow
        >
          <motion.div
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <Box
              p={3}
              border="1px"
              borderColor={borderColor}
              borderRadius="md"
              cursor="grab"
              _hover={{ bg: hoverBg }}
              _active={{ cursor: 'grabbing' }}
              mb={2}
              draggable
              onDragStart={(e) => handleDragStart(e, template)}
            >
              <VStack spacing={2} align="stretch">
                {/* Thumbnail */}
                <Box
                  h="60px"
                  bg="gray.100"
                  borderRadius="sm"
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                  overflow="hidden"
                >
                  <Image
                    src={template.thumbnail}
                    alt={template.name}
                    fallback={
                      <Text fontSize="xs" color="gray.500">
                        {template.type.toUpperCase()}
                      </Text>
                    }
                    objectFit="cover"
                    w="100%"
                    h="100%"
                  />
                </Box>
                
                {/* Section Info */}
                <Box>
                  <Text fontSize="sm" fontWeight="medium" noOfLines={1}>
                    {template.name}
                  </Text>
                  <Text fontSize="xs" color="gray.500" noOfLines={2}>
                    {template.description}
                  </Text>
                </Box>
              </VStack>
            </Box>
          </motion.div>
        </Tooltip>
      ))}
    </VStack>
  )
}
