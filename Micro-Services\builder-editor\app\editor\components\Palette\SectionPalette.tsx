'use client'

import {
  Box,
  VStack,
  Text,
  Image,
  useColorModeValue,
  Tooltip,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Badge,
  SimpleGrid
} from '@chakra-ui/react'
import { motion } from 'framer-motion'
import { sectionCategories, getSectionsByCategory, type SectionDesign } from '../../../../lib/sections/sectionDesigns'



export function SectionPalette() {
  const borderColor = useColorModeValue('gray.200', 'gray.700')
  const hoverBg = useColorModeValue('gray.50', 'gray.700')

  const handleDragStart = (e: React.DragEvent, design: SectionDesign) => {
    e.dataTransfer.setData('application/json', JSON.stringify({
      type: 'section',
      sectionType: design.category,
      name: design.name,
      defaultStyle: design.defaultStyle,
      defaultProps: design.defaultProps,
      designId: design.id
    }))
  }

  return (
    <VStack spacing={0} align="stretch" p={3}>
      <Text fontSize="sm" fontWeight="semibold" color="gray.600" mb={3}>
        Drag sections to canvas
      </Text>

      <Accordion allowMultiple defaultIndex={[0]}>
        {sectionCategories.map((category) => {
          const designs = getSectionsByCategory(category.id)

          return (
            <AccordionItem key={category.id} border="none">
              <AccordionButton px={0} py={2}>
                <Box flex="1" textAlign="left">
                  <Text fontSize="sm" fontWeight="medium">
                    {category.name}
                  </Text>
                  <Text fontSize="xs" color="gray.500">
                    {category.description}
                  </Text>
                </Box>
                <AccordionIcon />
              </AccordionButton>

              <AccordionPanel px={0} pb={4}>
                <VStack spacing={2} align="stretch">
                  {designs.map((design) => (
                    <Tooltip
                      key={design.id}
                      label={design.description}
                      placement="right"
                      hasArrow
                    >
                      <motion.div
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <Box
                          p={3}
                          border="1px"
                          borderColor={borderColor}
                          borderRadius="md"
                          cursor="grab"
                          _hover={{ bg: hoverBg }}
                          _active={{ cursor: 'grabbing' }}
                          mb={2}
                          draggable
                          onDragStart={(e) => handleDragStart(e, design)}
                        >
                          <VStack spacing={2} align="stretch">
                            {/* Thumbnail */}
                            <Box
                              h="80px"
                              bg="gray.100"
                              borderRadius="sm"
                              display="flex"
                              alignItems="center"
                              justifyContent="center"
                              overflow="hidden"
                              position="relative"
                            >
                              <Image
                                src={design.thumbnail}
                                alt={design.name}
                                fallback={
                                  <Text fontSize="xs" color="gray.500" textAlign="center">
                                    {design.category.toUpperCase()}
                                  </Text>
                                }
                                objectFit="cover"
                                w="100%"
                                h="100%"
                              />
                              <Badge
                                position="absolute"
                                top={1}
                                right={1}
                                size="sm"
                                colorScheme="blue"
                                fontSize="xs"
                              >
                                {design.category}
                              </Badge>
                            </Box>

                            {/* Section Info */}
                            <Box>
                              <Text fontSize="sm" fontWeight="medium" noOfLines={1}>
                                {design.name}
                              </Text>
                              <Text fontSize="xs" color="gray.500" noOfLines={2}>
                                {design.description}
                              </Text>
                            </Box>
                          </VStack>
                        </Box>
                      </motion.div>
                    </Tooltip>
                  ))}
                </VStack>
              </AccordionPanel>
            </AccordionItem>
          )
        })}
      </Accordion>
    </VStack>
  )
}
