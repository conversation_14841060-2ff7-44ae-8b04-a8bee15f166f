import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/utils/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()

    // Get the request body
    const body = await request.json()
    const { pageId, pageData, timestamp } = body

    if (!pageId || !pageData) {
      return NextResponse.json(
        { error: 'Missing required fields: pageId and pageData' },
        { status: 400 }
      )
    }

    // Check if user is authenticated
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check if page exists
    const { data: existingPage, error: fetchError } = await supabase
      .from('pages')
      .select('id, user_id')
      .eq('id', pageId)
      .single()

    if (fetchError && fetchError.code !== 'PGRST116') {
      console.error('Error fetching page:', fetchError)
      return NextResponse.json(
        { error: 'Database error' },
        { status: 500 }
      )
    }

    // If page exists, check ownership
    if (existingPage && existingPage.user_id !== user.id) {
      return NextResponse.json(
        { error: 'Forbidden: You do not own this page' },
        { status: 403 }
      )
    }

    // Prepare page data for database
    const pageRecord = {
      id: pageId,
      user_id: user.id,
      name: pageData.name || 'Untitled Page',
      slug: pageData.slug || 'untitled-page',
      content: pageData,
      seo_title: pageData.seoSettings?.title || pageData.name,
      seo_description: pageData.seoSettings?.description || '',
      seo_keywords: pageData.seoSettings?.keywords || [],
      is_published: pageData.settings?.isPublished || false,
      updated_at: new Date().toISOString()
    }

    let result

    if (existingPage) {
      // Update existing page
      const { data, error } = await supabase
        .from('pages')
        .update(pageRecord)
        .eq('id', pageId)
        .select()
        .single()

      if (error) {
        console.error('Error updating page:', error)
        return NextResponse.json(
          { error: 'Failed to update page' },
          { status: 500 }
        )
      }

      result = data
    } else {
      // Create new page
      pageRecord.created_at = new Date().toISOString()
      
      const { data, error } = await supabase
        .from('pages')
        .insert(pageRecord)
        .select()
        .single()

      if (error) {
        console.error('Error creating page:', error)
        return NextResponse.json(
          { error: 'Failed to create page' },
          { status: 500 }
        )
      }

      result = data
    }

    // Save page history for version control
    try {
      await supabase
        .from('page_history')
        .insert({
          page_id: pageId,
          user_id: user.id,
          content: pageData,
          version_note: 'Auto-save',
          created_at: new Date().toISOString()
        })
    } catch (historyError) {
      // Don't fail the save if history fails
      console.warn('Failed to save page history:', historyError)
    }

    return NextResponse.json({
      success: true,
      page: result,
      message: existingPage ? 'Page updated successfully' : 'Page created successfully',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error in save API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
