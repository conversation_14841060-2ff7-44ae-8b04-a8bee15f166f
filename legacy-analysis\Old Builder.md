# 🧱 Old Builder — Legacy Monolith Architecture Documentation (Generated 2025-05-31)

## 📦 Codebase Overview

```
/
├── builder-frontend-master/       # Main React builder interface
├── builder-backend-master/        # Backend GraphQL and REST APIs
├── builder-services-main/         # Internal logic & tools (templates, domains)
├── infra-services-main/           # DNS, deployment, infrastructure
├── sites-serve-main/              # Runtime SSR/CSR site rendering
├── wuilt-client-master/           # Legacy UI module (Wuilt-based)
├── backoffice-frontend-main/      # Support team frontend
├── backoffice-services-main/      # Internal support APIs
```

---

## 🖼️ Frontend Overview

### ➕ Builder Editor
- Framework: React + Chakra UI + Vite
- Features:
  - Drag & drop builder
  - Template system
  - Preview mode
  - Arabic & RTL layout
- Auth: Supabase JWT
- API: REST and GraphQL
- Layout: Responsive, modular, dynamic rendering

---

## 🔧 Backend API Overview

### 🧠 builder-backend-master
- Stack: Node.js + Express + Prisma
- Services:
  - User sessions
  - Pages & Sites
  - Templates
  - Domain registration
  - Stripe billing
- Middleware: Supabase JWT validation
- File upload: Supabase Storage

---

## 🗃️ Data Model (Prisma/Supabase)

- `User`: metadata, preferences, language
- `Site`: title, owner_id, domain, status
- `Page`: blocks[], layout config
- `Template`: category, visibility, content[]
- `Media`: user_id, type, path
- `Plan`: tier, pricing, features
- `Submission`: form_id, answers[], created_at

---

## 🛠️ Internal Tools

### Templates Service
- API to fetch/add template definitions
- Categories + filters
- Personal vs Public templates

### Publish Service
- Deploy site to domain/subdomain
- Tracks status & deploy metadata

### Questionnaire
- Dynamic form builder
- Submission logging
- Supabase row-level security

---

## 🧩 Support Backoffice

- Used by internal team only (support role)
- Tools:
  - Search Users / Sites
  - View/Reset Forms
  - Stripe Plan Management
  - Impersonate User Sessions
- Hosted: Vercel
- Auth: Supabase role validation

---

## 🔐 Supabase Integration

- RLS for `site`, `media`, `submission`
- JWT metadata tracks role: user/support
- Storage buckets for uploads
- Auth via Supabase Auth module

---

## 🌐 Routing & Deployment

| App/Service           | URL                             | Auth | Platform |
|----------------------|----------------------------------|------|----------|
| Builder Editor       | builder.yourdomain.com          | ✅   | Vercel   |
| Site Runtime         | *.yourdomain.com                | ❌   | Vercel   |
| Dashboard            | dashboard.yourdomain.com        | ✅   | Vercel   |
| Support UI           | support.yourdomain.com          | ✅   | Vercel   |
| Internal APIs        | support.yourdomain.com/api      | ✅   | Vercel   |

---

## 🧭 Mermaid Diagram: High-Level System

```mermaid
graph TD
  A[User] -->|Login| B(Supabase Auth)
  B --> C[Builder Editor]
  C --> D[Builder API]
  D --> E[Templates DB]
  D --> F[Media Storage]
  D --> G[Publish Engine]
  G --> H[Live Site *.yourdomain.com]
```

---

## ✅ Summary

This document reflects the current structure of the legacy builder system before migration.

To proceed: migrate each domain into microservice folders under `/services`, and align each with Supabase + Vercel deployment.

