'use client'

import {
  Box,
  VStack,
  Text,
  Image,
  Button,
  useColorModeValue,
  Badge,
  Flex
} from '@chakra-ui/react'
import { motion } from 'framer-motion'
import { useEditorStore } from '@/lib/stores/editorStore'

interface Template {
  id: string
  name: string
  description: string
  category: 'business' | 'portfolio' | 'blog' | 'ecommerce' | 'landing'
  thumbnail: string
  isPremium: boolean
  sections: any[]
}

// Template library based on Old Builder analysis
const templates: Template[] = [
  {
    id: 'business-1',
    name: 'Corporate Website',
    description: 'Professional business template with hero, services, and contact',
    category: 'business',
    thumbnail: '/api/placeholder-image?width=400&height=300&text=Corporate%20Website&bg=2563eb&color=ffffff',
    isPremium: false,
    sections: [
      {
        id: 'hero-section',
        type: 'hero',
        name: 'Hero Section',
        elements: [],
        style: {
          padding: '100px 20px',
          backgroundColor: '#1a202c',
          color: 'white'
        },
        responsive: { desktop: {}, tablet: {}, mobile: {} }
      },
      {
        id: 'services-section',
        type: 'feature',
        name: 'Our Services',
        elements: [],
        style: {
          padding: '80px 20px',
          backgroundColor: '#ffffff'
        },
        responsive: { desktop: {}, tablet: {}, mobile: {} }
      },
      {
        id: 'contact-section',
        type: 'contact',
        name: 'Contact Us',
        elements: [],
        style: {
          padding: '80px 20px',
          backgroundColor: '#f7fafc'
        },
        responsive: { desktop: {}, tablet: {}, mobile: {} }
      }
    ]
  },
  {
    id: 'portfolio-1',
    name: 'Creative Portfolio',
    description: 'Showcase your work with this modern portfolio template',
    category: 'portfolio',
    thumbnail: '/api/placeholder-image?width=400&height=300&text=Creative%20Portfolio&bg=7c3aed&color=ffffff',
    isPremium: true,
    sections: [
      {
        id: 'hero-section',
        type: 'hero',
        name: 'Welcome',
        elements: [],
        style: {
          padding: '120px 20px',
          backgroundColor: '#2d3748',
          color: 'white'
        },
        responsive: { desktop: {}, tablet: {}, mobile: {} }
      },
      {
        id: 'gallery-section',
        type: 'gallery',
        name: 'Portfolio',
        elements: [],
        style: {
          padding: '80px 20px',
          backgroundColor: '#ffffff'
        },
        responsive: { desktop: {}, tablet: {}, mobile: {} }
      }
    ]
  },
  {
    id: 'landing-1',
    name: 'Product Landing',
    description: 'Convert visitors with this high-converting landing page',
    category: 'landing',
    thumbnail: '/api/placeholder-image?width=400&height=300&text=Product%20Landing&bg=059669&color=ffffff',
    isPremium: false,
    sections: [
      {
        id: 'hero-section',
        type: 'hero',
        name: 'Hero',
        elements: [],
        style: {
          padding: '100px 20px',
          backgroundColor: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white'
        },
        responsive: { desktop: {}, tablet: {}, mobile: {} }
      },
      {
        id: 'features-section',
        type: 'feature',
        name: 'Features',
        elements: [],
        style: {
          padding: '80px 20px',
          backgroundColor: '#ffffff'
        },
        responsive: { desktop: {}, tablet: {}, mobile: {} }
      },
      {
        id: 'testimonials-section',
        type: 'testimonial',
        name: 'Testimonials',
        elements: [],
        style: {
          padding: '80px 20px',
          backgroundColor: '#f7fafc'
        },
        responsive: { desktop: {}, tablet: {}, mobile: {} }
      }
    ]
  }
]

export function TemplatePalette() {
  const borderColor = useColorModeValue('gray.200', 'gray.700')
  const hoverBg = useColorModeValue('gray.50', 'gray.700')
  
  const { setCurrentPage } = useEditorStore()

  const handleApplyTemplate = (template: Template) => {
    const newPage = {
      id: `page-${Date.now()}`,
      name: template.name,
      slug: template.name.toLowerCase().replace(/\s+/g, '-'),
      sections: template.sections,
      seoSettings: {
        title: template.name,
        description: template.description,
        keywords: [template.category, 'website', 'template']
      },
      settings: {}
    }
    
    setCurrentPage(newPage)
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'business': return 'blue'
      case 'portfolio': return 'purple'
      case 'blog': return 'green'
      case 'ecommerce': return 'orange'
      case 'landing': return 'red'
      default: return 'gray'
    }
  }

  return (
    <VStack spacing={0} align="stretch" p={3}>
      <Text fontSize="sm" fontWeight="semibold" color="gray.600" mb={3}>
        Choose a template to start
      </Text>
      
      {templates.map((template) => (
        <motion.div
          key={template.id}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <Box
            p={3}
            border="1px"
            borderColor={borderColor}
            borderRadius="md"
            _hover={{ bg: hoverBg }}
            mb={3}
          >
            <VStack spacing={3} align="stretch">
              {/* Template Thumbnail */}
              <Box
                h="120px"
                bg="gray.100"
                borderRadius="md"
                overflow="hidden"
                position="relative"
              >
                <Image
                  src={template.thumbnail}
                  alt={template.name}
                  fallback={
                    <Flex
                      h="100%"
                      align="center"
                      justify="center"
                      bg="gray.100"
                    >
                      <Text fontSize="sm" color="gray.500">
                        {template.name}
                      </Text>
                    </Flex>
                  }
                  objectFit="cover"
                  w="100%"
                  h="100%"
                />
                
                {template.isPremium && (
                  <Badge
                    position="absolute"
                    top="2"
                    right="2"
                    colorScheme="yellow"
                    variant="solid"
                    fontSize="xs"
                  >
                    PRO
                  </Badge>
                )}
              </Box>
              
              {/* Template Info */}
              <Box>
                <Flex align="center" justify="space-between" mb={1}>
                  <Text fontSize="sm" fontWeight="semibold" noOfLines={1}>
                    {template.name}
                  </Text>
                  <Badge
                    colorScheme={getCategoryColor(template.category)}
                    variant="subtle"
                    fontSize="xs"
                  >
                    {template.category}
                  </Badge>
                </Flex>
                
                <Text fontSize="xs" color="gray.500" noOfLines={2} mb={3}>
                  {template.description}
                </Text>
                
                <Button
                  size="sm"
                  colorScheme="blue"
                  variant="outline"
                  w="100%"
                  onClick={() => handleApplyTemplate(template)}
                >
                  Use Template
                </Button>
              </Box>
            </VStack>
          </Box>
        </motion.div>
      ))}
    </VStack>
  )
}
