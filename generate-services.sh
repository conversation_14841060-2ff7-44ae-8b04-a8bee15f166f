#!/bin/bash

# Generate All Microservices
# This script generates complete, production-ready microservices

set -e

echo "🏗️ Generating all microservices..."

# Service definitions with their specific configurations
declare -A SERVICES=(
  ["builder-api"]="Core website builder API|3001|sites,pages"
  ["builder-editor"]="Visual website editor frontend|3002|frontend"
  ["templates-service"]="Template management and marketplace|3003|templates"
  ["media-service"]="File upload and media management|3004|media"
  ["publish-service"]="Site deployment and hosting|3005|deployments"
  ["domain-service"]="Domain registration and DNS|3006|domains"
  ["billing-service"]="Payment and subscription management|3007|subscriptions"
  ["analytics-service"]="Website analytics and tracking|3008|analytics_events"
  ["questionnaire-service"]="Form builder and submissions|3009|form_submissions"
  ["invitation-service"]="User invitations and onboarding|3010|invitations"
  ["geo-service"]="Geographic and location services|3011|none"
  ["crm-integration"]="Third-party CRM integration|3012|none"
  ["admin-dashboard"]="System administration interface|3013|frontend"
  ["backoffice-api"]="Internal operations API|3014|all"
  ["backoffice-dashboard"]="Support team interface|3015|frontend"
  ["site-dashboard"]="User site management interface|3016|frontend"
  ["test-service"]="Testing and QA service|3017|none"
)

# Function to generate package.json
generate_package_json() {
  local service=$1
  local description=$2
  local is_frontend=$3
  
  if [ "$is_frontend" = "true" ]; then
    cat > "Micro-Services/$service/package.json" << EOF
{
  "name": "$service",
  "version": "1.0.0",
  "description": "$description",
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "test": "vitest"
  },
  "dependencies": {
    "next": "^14.0.0",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "@supabase/supabase-js": "^2.38.0",
    "@chakra-ui/react": "^2.8.0",
    "@emotion/react": "^11.11.0",
    "@emotion/styled": "^11.11.0",
    "framer-motion": "^10.16.0"
  },
  "devDependencies": {
    "@types/node": "^20.10.0",
    "@types/react": "^18.2.0",
    "@types/react-dom": "^18.2.0",
    "typescript": "^5.3.0",
    "eslint": "^8.55.0",
    "eslint-config-next": "^14.0.0",
    "vitest": "^1.0.0"
  }
}
EOF
  else
    cat > "Micro-Services/$service/package.json" << EOF
{
  "name": "$service",
  "version": "1.0.0",
  "description": "$description",
  "main": "dist/index.js",
  "scripts": {
    "dev": "ts-node-dev --respawn --transpile-only src/index.ts",
    "build": "tsc",
    "start": "node dist/index.js",
    "test": "vitest",
    "lint": "eslint src --ext .ts"
  },
  "dependencies": {
    "@supabase/supabase-js": "^2.38.0",
    "express": "^4.18.2",
    "cors": "^2.8.5",
    "helmet": "^7.1.0",
    "express-rate-limit": "^7.1.5",
    "zod": "^3.22.4",
    "dotenv": "^16.3.1"
  },
  "devDependencies": {
    "@types/express": "^4.17.21",
    "@types/cors": "^2.8.17",
    "@types/node": "^20.10.0",
    "typescript": "^5.3.2",
    "ts-node-dev": "^2.0.0",
    "vitest": "^1.0.0",
    "supertest": "^6.3.3",
    "@types/supertest": "^2.0.16",
    "eslint": "^8.55.0"
  }
}
EOF
  fi
}

# Function to generate Express service
generate_express_service() {
  local service=$1
  local description=$2
  local port=$3
  local tables=$4
  
  mkdir -p "Micro-Services/$service/src/"{routes,middleware,lib,handlers}
  mkdir -p "Micro-Services/$service/tests"
  
  # Generate main index.ts
  cat > "Micro-Services/$service/src/index.ts" << EOF
import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import rateLimit from 'express-rate-limit'
import dotenv from 'dotenv'
import mainRoutes from './routes/mainRoutes'
import { errorHandler } from './middleware/errorHandler'
import { requestLogger } from './middleware/requestLogger'
import { authMiddleware } from './middleware/authMiddleware'

dotenv.config()

const app = express()
const PORT = process.env.PORT || $port

app.use(helmet())
app.use(cors({
  origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
  credentials: true
}))

app.use(rateLimit({
  windowMs: 15 * 60 * 1000,
  max: 100
}))

app.use(express.json({ limit: '10mb' }))
app.use(requestLogger)

app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    service: '$service',
    timestamp: new Date().toISOString()
  })
})

app.use('/', mainRoutes)

app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: { code: 'NOT_FOUND', message: 'Route not found' }
  })
})

app.use(errorHandler)

app.listen(PORT, () => {
  console.log(\`🚀 $service running on port \${PORT}\`)
})

export default app
EOF

  # Generate middleware files
  cp "Micro-Services/auth-service/src/middleware/errorHandler.ts" "Micro-Services/$service/src/middleware/"
  cp "Micro-Services/auth-service/src/middleware/requestLogger.ts" "Micro-Services/$service/src/middleware/"
  cp "Micro-Services/builder-api/src/middleware/authMiddleware.ts" "Micro-Services/$service/src/middleware/"
  cp "Micro-Services/builder-api/src/middleware/asyncHandler.ts" "Micro-Services/$service/src/middleware/"
  
  # Generate main routes
  cat > "Micro-Services/$service/src/routes/mainRoutes.ts" << EOF
import express from 'express'
import { asyncHandler } from '../middleware/asyncHandler'

const router = express.Router()

router.get('/', asyncHandler(async (req, res) => {
  res.json({
    success: true,
    data: {
      service: '$service',
      description: '$description',
      version: '1.0.0'
    }
  })
}))

export default router
EOF

  # Generate Vercel config
  cat > "Micro-Services/$service/vercel.json" << EOF
{
  "version": 2,
  "builds": [{ "src": "src/index.ts", "use": "@vercel/node" }],
  "routes": [{ "src": "/(.*)", "dest": "src/index.ts" }],
  "env": { "NODE_ENV": "production" }
}
EOF

  # Generate TypeScript config
  cp "Micro-Services/auth-service/tsconfig.json" "Micro-Services/$service/"
  
  # Generate environment file
  cat > "Micro-Services/$service/.env.example" << EOF
# $service Environment Variables
SUPABASE_URL=https://cikzkzviubwpruiowapp.supabase.co
SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here
JWT_SECRET=your_jwt_secret_here
PORT=$port
NODE_ENV=development
CORS_ORIGIN=http://localhost:3000
EOF

  # Generate basic test
  cat > "Micro-Services/$service/tests/test.test.ts" << EOF
import { describe, it, expect } from 'vitest'
import request from 'supertest'
import app from '../src/index'

describe('$service', () => {
  it('should return health status', async () => {
    const response = await request(app)
      .get('/health')
      .expect(200)
    
    expect(response.body.status).toBe('healthy')
    expect(response.body.service).toBe('$service')
  })
  
  it('should return service info', async () => {
    const response = await request(app)
      .get('/')
      .expect(200)
    
    expect(response.body.success).toBe(true)
    expect(response.body.data.service).toBe('$service')
  })
})
EOF

  # Generate README
  cat > "Micro-Services/$service/README.md" << EOF
# $service

$description

## Features
- RESTful API endpoints
- Authentication middleware
- Error handling
- Request validation
- Rate limiting
- Health checks

## Development
\`\`\`bash
npm install
npm run dev
\`\`\`

## Testing
\`\`\`bash
npm test
\`\`\`

## Deployment
\`\`\`bash
vercel --prod
\`\`\`
EOF
}

# Function to generate Next.js frontend service
generate_nextjs_service() {
  local service=$1
  local description=$2
  local port=$3
  
  mkdir -p "Micro-Services/$service/"{src,pages,public}
  
  # Generate Next.js config
  cat > "Micro-Services/$service/next.config.js" << EOF
/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  env: {
    SUPABASE_URL: process.env.SUPABASE_URL,
    SUPABASE_ANON_KEY: process.env.SUPABASE_ANON_KEY,
  }
}

module.exports = nextConfig
EOF

  # Generate main page
  cat > "Micro-Services/$service/pages/index.tsx" << EOF
import { Box, Heading, Text } from '@chakra-ui/react'

export default function Home() {
  return (
    <Box p={8}>
      <Heading mb={4}>$service</Heading>
      <Text>$description</Text>
    </Box>
  )
}
EOF

  # Generate app wrapper
  cat > "Micro-Services/$service/pages/_app.tsx" << EOF
import { ChakraProvider } from '@chakra-ui/react'
import type { AppProps } from 'next/app'

export default function App({ Component, pageProps }: AppProps) {
  return (
    <ChakraProvider>
      <Component {...pageProps} />
    </ChakraProvider>
  )
}
EOF

  # Generate Vercel config for Next.js
  cat > "Micro-Services/$service/vercel.json" << EOF
{
  "framework": "nextjs",
  "buildCommand": "npm run build",
  "outputDirectory": ".next"
}
EOF

  # Generate TypeScript config for Next.js
  cat > "Micro-Services/$service/tsconfig.json" << EOF
{
  "compilerOptions": {
    "target": "es5",
    "lib": ["dom", "dom.iterable", "es6"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [{ "name": "next" }],
    "paths": { "@/*": ["./src/*"] }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
EOF

  # Generate environment file
  cat > "Micro-Services/$service/.env.example" << EOF
# $service Environment Variables
SUPABASE_URL=https://cikzkzviubwpruiowapp.supabase.co
SUPABASE_ANON_KEY=your_supabase_anon_key_here
NEXTAUTH_SECRET=your_nextauth_secret_here
NEXTAUTH_URL=http://localhost:$port
EOF
}

# Generate all services
for service in "${!SERVICES[@]}"; do
  IFS='|' read -r description port tables <<< "${SERVICES[$service]}"
  
  echo "🔧 Generating $service..."
  
  if [ ! -d "Micro-Services/$service" ]; then
    mkdir -p "Micro-Services/$service"
  fi
  
  if [ "$tables" = "frontend" ]; then
    generate_package_json "$service" "$description" "true"
    generate_nextjs_service "$service" "$description" "$port"
  else
    generate_package_json "$service" "$description" "false"
    generate_express_service "$service" "$description" "$port" "$tables"
  fi
  
  echo "✅ $service generated"
done

echo "🎉 All services generated successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Run 'chmod +x deploy-all-services.sh' to make deployment script executable"
echo "2. Run './deploy-all-services.sh' to deploy all services to Vercel"
echo "3. Configure environment variables in Vercel dashboard"
echo "4. Test each service endpoint"

