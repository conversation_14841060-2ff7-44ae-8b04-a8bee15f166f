'use client'

import { useState } from 'react'
import { 
  Box, 
  Flex, 
  Text, 
  IconButton, 
  useColorModeValue,
  Tooltip,
  Menu,
  MenuButton,
  MenuList,
  MenuItem
} from '@chakra-ui/react'
import { 
  DragHandleIcon, 
  SettingsIcon, 
  CopyIcon, 
  DeleteIcon,
  ChevronUpIcon,
  ChevronDownIcon
} from '@chakra-ui/icons'
import { motion } from 'framer-motion'
import { Section, useEditorStore } from '@/lib/stores/editorStore'
import { ElementRenderer } from './ElementRenderer'

interface SectionRendererProps {
  section: Section
  isSelected: boolean
  onSelect: () => void
}

export function SectionRenderer({ section, isSelected, onSelect }: SectionRendererProps) {
  const [isHovered, setIsHovered] = useState(false)
  const [isDragOver, setIsDragOver] = useState(false)

  const {
    currentBreakpoint,
    deleteSection,
    moveSection,
    currentPage,
    addSection,
    addElement
  } = useEditorStore()

  const borderColor = useColorModeValue('blue.400', 'blue.300')
  const hoverBorderColor = useColorModeValue('gray.300', 'gray.600')
  const toolbarBg = useColorModeValue('white', 'gray.800')
  const shadowColor = useColorModeValue('rgba(0,0,0,0.1)', 'rgba(0,0,0,0.3)')

  const handleDragStart = (e: React.DragEvent) => {
    e.dataTransfer.setData('application/json', JSON.stringify({
      type: 'move-section',
      sectionId: section.id
    }))
  }

  const handleDelete = () => {
    deleteSection(section.id)
  }

  const handleDuplicate = () => {
    const duplicatedSection = {
      ...section,
      id: `section-${Date.now()}`,
      name: `${section.name} (Copy)`
    }
    
    if (currentPage) {
      const currentIndex = currentPage.sections.findIndex(s => s.id === section.id)
      addSection(duplicatedSection, currentIndex + 1)
    }
  }

  const handleMoveUp = () => {
    if (currentPage) {
      const currentIndex = currentPage.sections.findIndex(s => s.id === section.id)
      if (currentIndex > 0) {
        moveSection(section.id, currentIndex - 1)
      }
    }
  }

  const handleMoveDown = () => {
    if (currentPage) {
      const currentIndex = currentPage.sections.findIndex(s => s.id === section.id)
      if (currentIndex < currentPage.sections.length - 1) {
        moveSection(section.id, currentIndex + 1)
      }
    }
  }

  // Element drop handlers
  const handleElementDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragOver(true)
  }

  const handleElementDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragOver(false)
  }

  const handleElementDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragOver(false)

    const dragData = e.dataTransfer.getData('application/json')
    if (dragData) {
      try {
        const data = JSON.parse(dragData)

        if (data.type === 'element') {
          // Create new element from palette
          const newElement = {
            id: `element-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            type: data.elementType,
            name: data.name,
            props: data.defaultProps || {},
            style: data.defaultStyle || {},
            responsive: {
              desktop: {},
              tablet: {},
              mobile: {}
            }
          }

          addElement(newElement, section.id)
        }
      } catch (error) {
        console.error('Error parsing element drag data:', error)
      }
    }
  }

  // Get responsive styles
  const responsiveStyle = section.responsive[currentBreakpoint] || {}
  const combinedStyle = { ...section.style, ...responsiveStyle }

  return (
    <Box
      position="relative"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={(e) => {
        e.stopPropagation()
        onSelect()
      }}
    >
      {/* Section Toolbar */}
      {(isSelected || isHovered) && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          style={{
            position: 'absolute',
            top: '-40px',
            left: '0',
            zIndex: 10,
            backgroundColor: toolbarBg,
            border: `1px solid ${borderColor}`,
            borderRadius: '6px',
            padding: '4px',
            boxShadow: `0 2px 8px ${shadowColor}`,
            display: 'flex',
            alignItems: 'center',
            gap: '4px'
          }}
        >
          <Tooltip label="Drag to move">
            <IconButton
              aria-label="Drag section"
              icon={<DragHandleIcon />}
              size="sm"
              variant="ghost"
              cursor="grab"
              draggable
              onDragStart={handleDragStart}
              _active={{ cursor: 'grabbing' }}
            />
          </Tooltip>
          
          <Text fontSize="xs" fontWeight="medium" color="gray.600" px={2}>
            {section.name}
          </Text>
          
          <Tooltip label="Move up">
            <IconButton
              aria-label="Move up"
              icon={<ChevronUpIcon />}
              size="sm"
              variant="ghost"
              onClick={handleMoveUp}
            />
          </Tooltip>
          
          <Tooltip label="Move down">
            <IconButton
              aria-label="Move down"
              icon={<ChevronDownIcon />}
              size="sm"
              variant="ghost"
              onClick={handleMoveDown}
            />
          </Tooltip>
          
          <Tooltip label="Duplicate">
            <IconButton
              aria-label="Duplicate section"
              icon={<CopyIcon />}
              size="sm"
              variant="ghost"
              onClick={handleDuplicate}
            />
          </Tooltip>
          
          <Menu>
            <MenuButton
              as={IconButton}
              aria-label="Section settings"
              icon={<SettingsIcon />}
              size="sm"
              variant="ghost"
            />
            <MenuList>
              <MenuItem onClick={handleDuplicate}>
                <CopyIcon mr={2} />
                Duplicate
              </MenuItem>
              <MenuItem onClick={handleDelete} color="red.500">
                <DeleteIcon mr={2} />
                Delete
              </MenuItem>
            </MenuList>
          </Menu>
        </motion.div>
      )}

      {/* Section Content */}
      <Box
        border={isSelected ? `2px solid ${borderColor}` : isHovered ? `1px solid ${hoverBorderColor}` : 'none'}
        borderRadius={isSelected || isHovered ? 'md' : 'none'}
        position="relative"
        style={combinedStyle}
        minH="100px"
        onDragOver={handleElementDragOver}
        onDragLeave={handleElementDragLeave}
        onDrop={handleElementDrop}
        bg={isDragOver ? 'blue.50' : 'transparent'}
        transition="background-color 0.2s"
      >
        {/* Section Elements */}
        {section.elements.length === 0 ? (
          <Flex
            minH="100px"
            align="center"
            justify="center"
            border="2px dashed"
            borderColor={isDragOver ? "blue.400" : "gray.300"}
            borderRadius="md"
            m={4}
            bg={isDragOver ? "blue.50" : "transparent"}
            transition="all 0.2s"
          >
            <Text color={isDragOver ? "blue.600" : "gray.500"} fontSize="sm">
              {isDragOver ? "Drop element here" : "Drop elements here or click to add content"}
            </Text>
          </Flex>
        ) : (
          <Box>
            {section.elements.map((element) => (
              <ElementRenderer
                key={element.id}
                element={element}
                sectionId={section.id}
              />
            ))}
          </Box>
        )}
      </Box>
    </Box>
  )
}
