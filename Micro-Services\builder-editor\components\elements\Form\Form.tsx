'use client'

import React, { useState } from 'react'
import {
  Box,
  VStack,
  HStack,
  Text,
  Input,
  Textarea,
  Select,
  Checkbox,
  Radio,
  RadioGroup,
  Button,
  FormControl,
  FormLabel,
  FormErrorMessage,
  IconButton,
  useColorModeValue,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Divider
} from '@chakra-ui/react'
import { AddIcon, DeleteIcon, DragHandleIcon } from '@chakra-ui/icons'

export interface FormField {
  id: string
  type: 'text' | 'email' | 'tel' | 'textarea' | 'select' | 'checkbox' | 'radio' | 'file'
  label: string
  placeholder?: string
  required: boolean
  options?: string[] // For select, radio, checkbox
  validation?: {
    minLength?: number
    maxLength?: number
    pattern?: string
    message?: string
  }
  width: 'full' | 'half' | 'third'
}

export interface FormSettings {
  title: string
  description?: string
  fields: FormField[]
  submitButton: {
    text: string
    color: string
    size: 'sm' | 'md' | 'lg'
  }
  styling: {
    backgroundColor: string
    borderColor: string
    borderRadius: string
    padding: string
    spacing: string
  }
  behavior: {
    action: string // URL or email
    method: 'POST' | 'GET'
    redirectUrl?: string
    successMessage: string
    errorMessage: string
  }
}

interface FormProps {
  settings: FormSettings
  onChange?: (settings: FormSettings) => void
  isEditing?: boolean
  className?: string
  style?: React.CSSProperties
}

export function Form({
  settings,
  onChange,
  isEditing = false,
  className,
  style
}: FormProps) {
  const [formData, setFormData] = useState<Record<string, any>>({})
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  const borderColor = useColorModeValue('gray.200', 'gray.700')
  const bgColor = useColorModeValue('white', 'gray.800')

  const handleFieldChange = (fieldId: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [fieldId]: value
    }))
    
    // Clear error when user starts typing
    if (errors[fieldId]) {
      setErrors(prev => ({
        ...prev,
        [fieldId]: ''
      }))
    }
  }

  const validateField = (field: FormField, value: any): string => {
    if (field.required && (!value || value.toString().trim() === '')) {
      return `${field.label} is required`
    }

    if (field.validation) {
      const { minLength, maxLength, pattern, message } = field.validation
      const stringValue = value?.toString() || ''

      if (minLength && stringValue.length < minLength) {
        return message || `${field.label} must be at least ${minLength} characters`
      }

      if (maxLength && stringValue.length > maxLength) {
        return message || `${field.label} must be no more than ${maxLength} characters`
      }

      if (pattern && !new RegExp(pattern).test(stringValue)) {
        return message || `${field.label} format is invalid`
      }
    }

    // Email validation
    if (field.type === 'email' && value) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(value)) {
        return 'Please enter a valid email address'
      }
    }

    return ''
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validate all fields
    const newErrors: Record<string, string> = {}
    settings.fields.forEach(field => {
      const error = validateField(field, formData[field.id])
      if (error) {
        newErrors[field.id] = error
      }
    })

    setErrors(newErrors)

    if (Object.keys(newErrors).length > 0) {
      return
    }

    setIsSubmitting(true)

    try {
      // In a real implementation, this would submit to the configured action
      console.log('Form submission:', formData)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      alert(settings.behavior.successMessage || 'Form submitted successfully!')
      setFormData({})
    } catch (error) {
      alert(settings.behavior.errorMessage || 'An error occurred. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const renderField = (field: FormField) => {
    const value = formData[field.id] || ''
    const error = errors[field.id]

    const fieldProps = {
      id: field.id,
      placeholder: field.placeholder,
      value,
      onChange: (e: any) => handleFieldChange(field.id, e.target.value),
      isInvalid: !!error,
      size: 'md' as const
    }

    let fieldElement: React.ReactNode

    switch (field.type) {
      case 'text':
      case 'email':
      case 'tel':
        fieldElement = <Input type={field.type} {...fieldProps} />
        break

      case 'textarea':
        fieldElement = (
          <Textarea
            {...fieldProps}
            rows={4}
            resize="vertical"
          />
        )
        break

      case 'select':
        fieldElement = (
          <Select {...fieldProps}>
            <option value="">Select an option</option>
            {field.options?.map(option => (
              <option key={option} value={option}>
                {option}
              </option>
            ))}
          </Select>
        )
        break

      case 'radio':
        fieldElement = (
          <RadioGroup
            value={value}
            onChange={(val) => handleFieldChange(field.id, val)}
          >
            <VStack align="start" spacing={2}>
              {field.options?.map(option => (
                <Radio key={option} value={option}>
                  {option}
                </Radio>
              ))}
            </VStack>
          </RadioGroup>
        )
        break

      case 'checkbox':
        fieldElement = (
          <VStack align="start" spacing={2}>
            {field.options?.map(option => (
              <Checkbox
                key={option}
                isChecked={Array.isArray(value) && value.includes(option)}
                onChange={(e) => {
                  const currentValues = Array.isArray(value) ? value : []
                  const newValues = e.target.checked
                    ? [...currentValues, option]
                    : currentValues.filter(v => v !== option)
                  handleFieldChange(field.id, newValues)
                }}
              >
                {option}
              </Checkbox>
            ))}
          </VStack>
        )
        break

      case 'file':
        fieldElement = (
          <Input
            type="file"
            onChange={(e) => handleFieldChange(field.id, e.target.files?.[0])}
            p={1}
          />
        )
        break

      default:
        fieldElement = <Input {...fieldProps} />
    }

    const widthMap = {
      full: '100%',
      half: '50%',
      third: '33.333%'
    }

    return (
      <FormControl
        key={field.id}
        isRequired={field.required}
        isInvalid={!!error}
        width={widthMap[field.width]}
        minW="200px"
      >
        <FormLabel fontSize="sm" fontWeight="medium">
          {field.label}
        </FormLabel>
        {fieldElement}
        <FormErrorMessage fontSize="xs">{error}</FormErrorMessage>
      </FormControl>
    )
  }

  const formStyle: React.CSSProperties = {
    backgroundColor: settings.styling.backgroundColor || bgColor,
    borderColor: settings.styling.borderColor || borderColor,
    borderRadius: settings.styling.borderRadius || '8px',
    padding: settings.styling.padding || '24px',
    ...style
  }

  if (isEditing) {
    return (
      <Box
        className={className}
        style={formStyle}
        border="2px dashed"
        borderColor="blue.300"
        position="relative"
      >
        <Text fontSize="sm" color="blue.600" mb={2} fontWeight="medium">
          Form Component (Click to edit)
        </Text>
        
        {settings.title && (
          <Text fontSize="lg" fontWeight="bold" mb={2}>
            {settings.title}
          </Text>
        )}
        
        {settings.description && (
          <Text fontSize="sm" color="gray.600" mb={4}>
            {settings.description}
          </Text>
        )}

        <VStack spacing={4} align="stretch">
          {settings.fields.slice(0, 3).map(field => (
            <Box key={field.id} opacity={0.7}>
              {renderField(field)}
            </Box>
          ))}
          
          {settings.fields.length > 3 && (
            <Text fontSize="sm" color="gray.500" textAlign="center">
              ... and {settings.fields.length - 3} more fields
            </Text>
          )}
          
          <Button
            colorScheme={settings.submitButton.color}
            size={settings.submitButton.size}
            opacity={0.7}
            cursor="default"
          >
            {settings.submitButton.text}
          </Button>
        </VStack>
      </Box>
    )
  }

  return (
    <Box
      as="form"
      onSubmit={handleSubmit}
      className={className}
      style={formStyle}
      border="1px solid"
      borderColor={settings.styling.borderColor || borderColor}
    >
      {settings.title && (
        <Text fontSize="xl" fontWeight="bold" mb={2}>
          {settings.title}
        </Text>
      )}
      
      {settings.description && (
        <Text fontSize="sm" color="gray.600" mb={6}>
          {settings.description}
        </Text>
      )}

      <VStack spacing={settings.styling.spacing || '16px'} align="stretch">
        {/* Render fields in rows based on width */}
        {(() => {
          const rows: FormField[][] = []
          let currentRow: FormField[] = []
          let currentRowWidth = 0

          settings.fields.forEach(field => {
            const fieldWidth = field.width === 'full' ? 100 : field.width === 'half' ? 50 : 33.333
            
            if (currentRowWidth + fieldWidth > 100 || field.width === 'full') {
              if (currentRow.length > 0) {
                rows.push(currentRow)
                currentRow = []
                currentRowWidth = 0
              }
            }
            
            currentRow.push(field)
            currentRowWidth += fieldWidth
            
            if (field.width === 'full') {
              rows.push(currentRow)
              currentRow = []
              currentRowWidth = 0
            }
          })
          
          if (currentRow.length > 0) {
            rows.push(currentRow)
          }

          return rows.map((row, rowIndex) => (
            <HStack key={rowIndex} spacing={4} align="start" wrap="wrap">
              {row.map(renderField)}
            </HStack>
          ))
        })()}

        <Button
          type="submit"
          colorScheme={settings.submitButton.color}
          size={settings.submitButton.size}
          isLoading={isSubmitting}
          loadingText="Submitting..."
          mt={4}
        >
          {settings.submitButton.text}
        </Button>
      </VStack>
    </Box>
  )
}

export default Form
