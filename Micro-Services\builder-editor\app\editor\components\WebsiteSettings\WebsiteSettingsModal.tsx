'use client'

import React, { useState } from 'react'
import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  VStack,
  HStack,
  Text,
  Button,
  Box,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Grid,
  Input,
  FormControl,
  FormLabel,
  Select,
  useColorModeValue,
  Image,
  IconButton,
  Tooltip,
  Divider,
  Badge,
  useToast
} from '@chakra-ui/react'
import {
  AddIcon,
  DeleteIcon,
  AttachmentIcon,
  CheckIcon
} from '@chakra-ui/icons'
import { useTranslation } from '@/lib/contexts/LanguageContext'

interface WebsiteSettings {
  colors: {
    primary: string
    secondary: string
    accent: string
    background: string
    text: string
  }
  fonts: {
    heading: string
    body: string
  }
  buttons: {
    style: 'rounded' | 'square' | 'pill'
    size: 'sm' | 'md' | 'lg'
    variant: 'solid' | 'outline' | 'ghost'
  }
  logo: {
    url: string
    alt: string
    width: string
    height: string
  }
  favicon: {
    url: string
  }
}

interface WebsiteSettingsModalProps {
  isOpen: boolean
  onClose: () => void
  settings: WebsiteSettings
  onSave: (settings: WebsiteSettings) => void
}

const colorPalettes = [
  {
    name: 'Professional Blue',
    colors: {
      primary: '#2563eb',
      secondary: '#64748b',
      accent: '#0ea5e9',
      background: '#ffffff',
      text: '#1e293b'
    }
  },
  {
    name: 'Modern Purple',
    colors: {
      primary: '#7c3aed',
      secondary: '#a855f7',
      accent: '#c084fc',
      background: '#fafafa',
      text: '#374151'
    }
  },
  {
    name: 'Elegant Green',
    colors: {
      primary: '#059669',
      secondary: '#10b981',
      accent: '#34d399',
      background: '#ffffff',
      text: '#111827'
    }
  },
  {
    name: 'Warm Orange',
    colors: {
      primary: '#ea580c',
      secondary: '#fb923c',
      accent: '#fdba74',
      background: '#fffbeb',
      text: '#1c1917'
    }
  }
]

const fontOptions = [
  { name: 'Inter', value: 'Inter, sans-serif' },
  { name: 'Roboto', value: 'Roboto, sans-serif' },
  { name: 'Open Sans', value: 'Open Sans, sans-serif' },
  { name: 'Lato', value: 'Lato, sans-serif' },
  { name: 'Montserrat', value: 'Montserrat, sans-serif' },
  { name: 'Poppins', value: 'Poppins, sans-serif' },
  { name: 'Playfair Display', value: 'Playfair Display, serif' },
  { name: 'Merriweather', value: 'Merriweather, serif' }
]

export function WebsiteSettingsModal({ isOpen, onClose, settings, onSave }: WebsiteSettingsModalProps) {
  const [localSettings, setLocalSettings] = useState<WebsiteSettings>(settings)
  const [isUploading, setIsUploading] = useState(false)
  const { t } = useTranslation()
  const toast = useToast()
  
  const borderColor = useColorModeValue('gray.200', 'gray.700')

  const handleColorPaletteSelect = (palette: typeof colorPalettes[0]) => {
    setLocalSettings(prev => ({
      ...prev,
      colors: palette.colors
    }))
  }

  const handleColorChange = (colorKey: keyof WebsiteSettings['colors'], value: string) => {
    setLocalSettings(prev => ({
      ...prev,
      colors: {
        ...prev.colors,
        [colorKey]: value
      }
    }))
  }

  const handleFontChange = (fontType: 'heading' | 'body', value: string) => {
    setLocalSettings(prev => ({
      ...prev,
      fonts: {
        ...prev.fonts,
        [fontType]: value
      }
    }))
  }

  const handleButtonStyleChange = (key: keyof WebsiteSettings['buttons'], value: any) => {
    setLocalSettings(prev => ({
      ...prev,
      buttons: {
        ...prev.buttons,
        [key]: value
      }
    }))
  }

  const handleImageUpload = async (type: 'logo' | 'favicon', file: File) => {
    setIsUploading(true)
    
    try {
      const formData = new FormData()
      formData.append('file', file)

      const response = await fetch('/api/media/upload', {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) {
        throw new Error('Upload failed')
      }

      const result = await response.json()
      
      if (type === 'logo') {
        setLocalSettings(prev => ({
          ...prev,
          logo: {
            ...prev.logo,
            url: result.url,
            alt: result.filename || 'Logo'
          }
        }))
      } else {
        setLocalSettings(prev => ({
          ...prev,
          favicon: {
            url: result.url
          }
        }))
      }

      toast({
        title: `${type === 'logo' ? 'Logo' : 'Favicon'} uploaded`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      })

    } catch (error) {
      toast({
        title: 'Upload failed',
        description: 'Failed to upload image. Please try again.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      })
    } finally {
      setIsUploading(false)
    }
  }

  const handleSave = () => {
    onSave(localSettings)
    onClose()
    
    toast({
      title: 'Website settings saved',
      description: 'Your website customization has been applied',
      status: 'success',
      duration: 3000,
      isClosable: true,
    })
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="4xl">
      <ModalOverlay />
      <ModalContent maxW="800px" h="80vh">
        <ModalHeader>
          <Text fontSize="xl" fontWeight="bold">
            {t('website.settings')} - Website Customization
          </Text>
        </ModalHeader>
        <ModalCloseButton />
        
        <ModalBody pb={6}>
          <Tabs variant="enclosed" h="100%">
            <TabList>
              <Tab>{t('website.colors')}</Tab>
              <Tab>{t('website.fonts')}</Tab>
              <Tab>{t('website.buttons')}</Tab>
              <Tab>{t('website.logo')}</Tab>
            </TabList>

            <TabPanels h="calc(100% - 40px)" overflowY="auto">
              {/* Colors Tab */}
              <TabPanel>
                <VStack spacing={6} align="stretch">
                  <Box>
                    <Text fontSize="lg" fontWeight="semibold" mb={4}>
                      Color Palettes
                    </Text>
                    <Grid templateColumns="repeat(auto-fit, minmax(200px, 1fr))" gap={4}>
                      {colorPalettes.map((palette, index) => (
                        <Box
                          key={index}
                          p={4}
                          border="2px"
                          borderColor={borderColor}
                          borderRadius="md"
                          cursor="pointer"
                          _hover={{ borderColor: 'blue.500' }}
                          onClick={() => handleColorPaletteSelect(palette)}
                        >
                          <Text fontSize="sm" fontWeight="medium" mb={3}>
                            {palette.name}
                          </Text>
                          <HStack spacing={2}>
                            {Object.values(palette.colors).map((color, colorIndex) => (
                              <Box
                                key={colorIndex}
                                w="20px"
                                h="20px"
                                bg={color}
                                borderRadius="sm"
                                border="1px"
                                borderColor="gray.200"
                              />
                            ))}
                          </HStack>
                        </Box>
                      ))}
                    </Grid>
                  </Box>

                  <Divider />

                  <Box>
                    <Text fontSize="lg" fontWeight="semibold" mb={4}>
                      Custom Colors
                    </Text>
                    <Grid templateColumns="repeat(auto-fit, minmax(200px, 1fr))" gap={4}>
                      {Object.entries(localSettings.colors).map(([key, value]) => (
                        <FormControl key={key}>
                          <FormLabel fontSize="sm" textTransform="capitalize">
                            {key}
                          </FormLabel>
                          <HStack>
                            <Input
                              type="color"
                              value={value}
                              onChange={(e) => handleColorChange(key as keyof WebsiteSettings['colors'], e.target.value)}
                              w="60px"
                              h="40px"
                              p={1}
                            />
                            <Input
                              value={value}
                              onChange={(e) => handleColorChange(key as keyof WebsiteSettings['colors'], e.target.value)}
                              placeholder="#000000"
                              size="sm"
                            />
                          </HStack>
                        </FormControl>
                      ))}
                    </Grid>
                  </Box>
                </VStack>
              </TabPanel>

              {/* Fonts Tab */}
              <TabPanel>
                <VStack spacing={6} align="stretch">
                  <FormControl>
                    <FormLabel>{t('website.fonts')} - Heading</FormLabel>
                    <Select
                      value={localSettings.fonts.heading}
                      onChange={(e) => handleFontChange('heading', e.target.value)}
                    >
                      {fontOptions.map((font) => (
                        <option key={font.value} value={font.value}>
                          {font.name}
                        </option>
                      ))}
                    </Select>
                    <Text
                      mt={2}
                      fontSize="xl"
                      fontWeight="bold"
                      fontFamily={localSettings.fonts.heading}
                    >
                      Sample Heading Text
                    </Text>
                  </FormControl>

                  <FormControl>
                    <FormLabel>{t('website.fonts')} - Body</FormLabel>
                    <Select
                      value={localSettings.fonts.body}
                      onChange={(e) => handleFontChange('body', e.target.value)}
                    >
                      {fontOptions.map((font) => (
                        <option key={font.value} value={font.value}>
                          {font.name}
                        </option>
                      ))}
                    </Select>
                    <Text
                      mt={2}
                      fontFamily={localSettings.fonts.body}
                    >
                      Sample body text to preview the selected font family.
                    </Text>
                  </FormControl>
                </VStack>
              </TabPanel>

              {/* Buttons Tab */}
              <TabPanel>
                <VStack spacing={6} align="stretch">
                  <HStack spacing={6}>
                    <FormControl>
                      <FormLabel>Button Style</FormLabel>
                      <Select
                        value={localSettings.buttons.style}
                        onChange={(e) => handleButtonStyleChange('style', e.target.value)}
                      >
                        <option value="rounded">Rounded</option>
                        <option value="square">Square</option>
                        <option value="pill">Pill</option>
                      </Select>
                    </FormControl>

                    <FormControl>
                      <FormLabel>Button Size</FormLabel>
                      <Select
                        value={localSettings.buttons.size}
                        onChange={(e) => handleButtonStyleChange('size', e.target.value)}
                      >
                        <option value="sm">Small</option>
                        <option value="md">Medium</option>
                        <option value="lg">Large</option>
                      </Select>
                    </FormControl>

                    <FormControl>
                      <FormLabel>Button Variant</FormLabel>
                      <Select
                        value={localSettings.buttons.variant}
                        onChange={(e) => handleButtonStyleChange('variant', e.target.value)}
                      >
                        <option value="solid">Solid</option>
                        <option value="outline">Outline</option>
                        <option value="ghost">Ghost</option>
                      </Select>
                    </FormControl>
                  </HStack>

                  <Box>
                    <Text fontSize="sm" fontWeight="medium" mb={3}>
                      Button Preview
                    </Text>
                    <HStack spacing={4}>
                      <Button
                        size={localSettings.buttons.size}
                        variant={localSettings.buttons.variant}
                        borderRadius={
                          localSettings.buttons.style === 'rounded' ? 'md' :
                          localSettings.buttons.style === 'pill' ? 'full' : '0'
                        }
                        bg={localSettings.colors.primary}
                        color="white"
                        _hover={{ opacity: 0.8 }}
                      >
                        Primary Button
                      </Button>
                      <Button
                        size={localSettings.buttons.size}
                        variant="outline"
                        borderRadius={
                          localSettings.buttons.style === 'rounded' ? 'md' :
                          localSettings.buttons.style === 'pill' ? 'full' : '0'
                        }
                        borderColor={localSettings.colors.primary}
                        color={localSettings.colors.primary}
                      >
                        Secondary Button
                      </Button>
                    </HStack>
                  </Box>
                </VStack>
              </TabPanel>

              {/* Logo & Favicon Tab */}
              <TabPanel>
                <VStack spacing={6} align="stretch">
                  <Box>
                    <Text fontSize="lg" fontWeight="semibold" mb={4}>
                      {t('website.logo')}
                    </Text>
                    <HStack spacing={4} align="start">
                      <Box>
                        {localSettings.logo.url ? (
                          <Image
                            src={localSettings.logo.url}
                            alt={localSettings.logo.alt}
                            maxW="200px"
                            maxH="100px"
                            objectFit="contain"
                            border="1px"
                            borderColor={borderColor}
                            borderRadius="md"
                            p={2}
                          />
                        ) : (
                          <Box
                            w="200px"
                            h="100px"
                            border="2px dashed"
                            borderColor={borderColor}
                            borderRadius="md"
                            display="flex"
                            alignItems="center"
                            justifyContent="center"
                          >
                            <Text fontSize="sm" color="gray.500">
                              No logo uploaded
                            </Text>
                          </Box>
                        )}
                      </Box>
                      <VStack align="start">
                        <Button
                          leftIcon={<AttachmentIcon />}
                          size="sm"
                          isLoading={isUploading}
                          onClick={() => {
                            const input = document.createElement('input')
                            input.type = 'file'
                            input.accept = 'image/*'
                            input.onchange = (e) => {
                              const file = (e.target as HTMLInputElement).files?.[0]
                              if (file) handleImageUpload('logo', file)
                            }
                            input.click()
                          }}
                        >
                          Upload Logo
                        </Button>
                        <Text fontSize="xs" color="gray.500">
                          Recommended: PNG, SVG, or JPG
                          <br />
                          Max size: 5MB
                        </Text>
                      </VStack>
                    </HStack>
                  </Box>

                  <Divider />

                  <Box>
                    <Text fontSize="lg" fontWeight="semibold" mb={4}>
                      {t('website.favicon')}
                    </Text>
                    <HStack spacing={4} align="start">
                      <Box>
                        {localSettings.favicon.url ? (
                          <Image
                            src={localSettings.favicon.url}
                            alt="Favicon"
                            w="32px"
                            h="32px"
                            objectFit="contain"
                            border="1px"
                            borderColor={borderColor}
                            borderRadius="sm"
                          />
                        ) : (
                          <Box
                            w="32px"
                            h="32px"
                            border="2px dashed"
                            borderColor={borderColor}
                            borderRadius="sm"
                            display="flex"
                            alignItems="center"
                            justifyContent="center"
                          >
                            <Text fontSize="xs" color="gray.500">
                              ?
                            </Text>
                          </Box>
                        )}
                      </Box>
                      <VStack align="start">
                        <Button
                          leftIcon={<AttachmentIcon />}
                          size="sm"
                          isLoading={isUploading}
                          onClick={() => {
                            const input = document.createElement('input')
                            input.type = 'file'
                            input.accept = 'image/*'
                            input.onchange = (e) => {
                              const file = (e.target as HTMLInputElement).files?.[0]
                              if (file) handleImageUpload('favicon', file)
                            }
                            input.click()
                          }}
                        >
                          Upload Favicon
                        </Button>
                        <Text fontSize="xs" color="gray.500">
                          Recommended: 32x32px ICO or PNG
                          <br />
                          Max size: 1MB
                        </Text>
                      </VStack>
                    </HStack>
                  </Box>
                </VStack>
              </TabPanel>
            </TabPanels>
          </Tabs>

          <HStack justify="flex-end" mt={6} pt={4} borderTop="1px" borderColor={borderColor}>
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button colorScheme="blue" onClick={handleSave} leftIcon={<CheckIcon />}>
              Save Settings
            </Button>
          </HStack>
        </ModalBody>
      </ModalContent>
    </Modal>
  )
}

export default WebsiteSettingsModal
