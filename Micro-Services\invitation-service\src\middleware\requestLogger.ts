import { Request, Response, NextFunction } from 'express'

export function requestLogger(req: Request, res: Response, next: NextFunction) {
  const start = Date.now()
  
  // Log request
  console.log(`📥 ${req.method} ${req.url} - ${req.ip}`)
  
  // Override res.end to log response
  const originalEnd = res.end
  res.end = function(chunk?: any, encoding?: any) {
    const duration = Date.now() - start
    const statusColor = res.statusCode >= 400 ? '🔴' : '🟢'
    
    console.log(`📤 ${statusColor} ${req.method} ${req.url} - ${res.statusCode} - ${duration}ms`)
    
    originalEnd.call(this, chunk, encoding)
  }
  
  next()
}

