'use client'

import React, { useState, useRef } from 'react'
import {
  Box,
  Image as ChakraImage,
  VStack,
  HStack,
  Text,
  Input,
  Select,
  Button,
  FormControl,
  FormLabel,
  Switch,
  Slider,
  SliderTrack,
  SliderFilledTrack,
  SliderThumb,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  useColorModeValue,
  IconButton,
  Tooltip,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel
} from '@chakra-ui/react'
import { EditIcon, DownloadIcon, SearchIcon } from '@chakra-ui/icons'

export interface ImageSettings {
  src: string
  alt: string
  width: string
  height: string
  objectFit: 'cover' | 'contain' | 'fill' | 'scale-down' | 'none'
  objectPosition: string
  borderRadius: string
  border: {
    width: string
    style: string
    color: string
  }
  shadow: {
    enabled: boolean
    x: number
    y: number
    blur: number
    spread: number
    color: string
  }
  filters: {
    brightness: number
    contrast: number
    saturation: number
    blur: number
    grayscale: number
    sepia: number
    hueRotate: number
  }
  overlay: {
    enabled: boolean
    color: string
    opacity: number
    blendMode: string
  }
  lazy: boolean
  clickAction: 'none' | 'lightbox' | 'link'
  linkUrl?: string
  lightboxCaption?: string
}

interface ImageProps {
  settings: ImageSettings
  onChange?: (settings: ImageSettings) => void
  isEditing?: boolean
  className?: string
  style?: React.CSSProperties
}

export function Image({
  settings,
  onChange,
  isEditing = false,
  className,
  style
}: ImageProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [showLightbox, setShowLightbox] = useState(false)
  const { isOpen, onOpen, onClose } = useDisclosure()
  const fileInputRef = useRef<HTMLInputElement>(null)
  
  const borderColor = useColorModeValue('gray.200', 'gray.700')
  const bgColor = useColorModeValue('gray.50', 'gray.800')

  const handleSettingsChange = (key: keyof ImageSettings, value: any) => {
    onChange?.({
      ...settings,
      [key]: value
    })
  }

  const handleNestedSettingsChange = (
    parent: keyof ImageSettings,
    key: string,
    value: any
  ) => {
    onChange?.({
      ...settings,
      [parent]: {
        ...(settings[parent] as any),
        [key]: value
      }
    })
  }

  const handleFileUpload = async (file: File) => {
    setIsLoading(true)
    setError(null)

    try {
      // In a real implementation, this would upload to your media service
      const formData = new FormData()
      formData.append('file', file)
      
      // Simulate upload
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Create object URL for preview (in real app, use uploaded URL)
      const objectUrl = URL.createObjectURL(file)
      handleSettingsChange('src', objectUrl)
    } catch (err) {
      setError('Failed to upload image')
    } finally {
      setIsLoading(false)
    }
  }

  const getImageStyle = (): React.CSSProperties => {
    const filters = []
    
    if (settings.filters.brightness !== 100) {
      filters.push(`brightness(${settings.filters.brightness}%)`)
    }
    if (settings.filters.contrast !== 100) {
      filters.push(`contrast(${settings.filters.contrast}%)`)
    }
    if (settings.filters.saturation !== 100) {
      filters.push(`saturate(${settings.filters.saturation}%)`)
    }
    if (settings.filters.blur > 0) {
      filters.push(`blur(${settings.filters.blur}px)`)
    }
    if (settings.filters.grayscale > 0) {
      filters.push(`grayscale(${settings.filters.grayscale}%)`)
    }
    if (settings.filters.sepia > 0) {
      filters.push(`sepia(${settings.filters.sepia}%)`)
    }
    if (settings.filters.hueRotate !== 0) {
      filters.push(`hue-rotate(${settings.filters.hueRotate}deg)`)
    }

    const boxShadow = settings.shadow.enabled
      ? `${settings.shadow.x}px ${settings.shadow.y}px ${settings.shadow.blur}px ${settings.shadow.spread}px ${settings.shadow.color}`
      : 'none'

    return {
      width: settings.width || 'auto',
      height: settings.height || 'auto',
      objectFit: settings.objectFit || 'cover',
      objectPosition: settings.objectPosition || 'center',
      borderRadius: settings.borderRadius || '0',
      border: settings.border.width 
        ? `${settings.border.width} ${settings.border.style} ${settings.border.color}`
        : 'none',
      boxShadow,
      filter: filters.length > 0 ? filters.join(' ') : 'none',
      cursor: settings.clickAction !== 'none' ? 'pointer' : 'default',
      ...style
    }
  }

  const handleImageClick = () => {
    if (settings.clickAction === 'lightbox') {
      setShowLightbox(true)
    } else if (settings.clickAction === 'link' && settings.linkUrl) {
      window.open(settings.linkUrl, '_blank')
    }
  }

  const renderImageEditor = () => (
    <Modal isOpen={isOpen} onClose={onClose} size="xl">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>Image Settings</ModalHeader>
        <ModalCloseButton />
        <ModalBody pb={6}>
          <Tabs>
            <TabList>
              <Tab>Source</Tab>
              <Tab>Appearance</Tab>
              <Tab>Effects</Tab>
              <Tab>Behavior</Tab>
            </TabList>

            <TabPanels>
              {/* Source Tab */}
              <TabPanel>
                <VStack spacing={4} align="stretch">
                  <FormControl>
                    <FormLabel fontSize="sm">Image URL</FormLabel>
                    <Input
                      value={settings.src}
                      onChange={(e) => handleSettingsChange('src', e.target.value)}
                      placeholder="https://example.com/image.jpg"
                      size="sm"
                    />
                  </FormControl>

                  <FormControl>
                    <FormLabel fontSize="sm">Upload Image</FormLabel>
                    <Input
                      ref={fileInputRef}
                      type="file"
                      accept="image/*"
                      onChange={(e) => {
                        const file = e.target.files?.[0]
                        if (file) handleFileUpload(file)
                      }}
                      size="sm"
                    />
                  </FormControl>

                  <FormControl>
                    <FormLabel fontSize="sm">Alt Text</FormLabel>
                    <Input
                      value={settings.alt}
                      onChange={(e) => handleSettingsChange('alt', e.target.value)}
                      placeholder="Image description"
                      size="sm"
                    />
                  </FormControl>
                </VStack>
              </TabPanel>

              {/* Appearance Tab */}
              <TabPanel>
                <VStack spacing={4} align="stretch">
                  <HStack>
                    <FormControl>
                      <FormLabel fontSize="sm">Width</FormLabel>
                      <Input
                        value={settings.width}
                        onChange={(e) => handleSettingsChange('width', e.target.value)}
                        placeholder="auto"
                        size="sm"
                      />
                    </FormControl>
                    <FormControl>
                      <FormLabel fontSize="sm">Height</FormLabel>
                      <Input
                        value={settings.height}
                        onChange={(e) => handleSettingsChange('height', e.target.value)}
                        placeholder="auto"
                        size="sm"
                      />
                    </FormControl>
                  </HStack>

                  <FormControl>
                    <FormLabel fontSize="sm">Object Fit</FormLabel>
                    <Select
                      value={settings.objectFit}
                      onChange={(e) => handleSettingsChange('objectFit', e.target.value)}
                      size="sm"
                    >
                      <option value="cover">Cover</option>
                      <option value="contain">Contain</option>
                      <option value="fill">Fill</option>
                      <option value="scale-down">Scale Down</option>
                      <option value="none">None</option>
                    </Select>
                  </FormControl>

                  <FormControl>
                    <FormLabel fontSize="sm">Border Radius</FormLabel>
                    <Input
                      value={settings.borderRadius}
                      onChange={(e) => handleSettingsChange('borderRadius', e.target.value)}
                      placeholder="0px"
                      size="sm"
                    />
                  </FormControl>

                  <FormControl display="flex" alignItems="center">
                    <FormLabel fontSize="sm" mb="0">Enable Shadow</FormLabel>
                    <Switch
                      isChecked={settings.shadow.enabled}
                      onChange={(e) => handleNestedSettingsChange('shadow', 'enabled', e.target.checked)}
                    />
                  </FormControl>

                  {settings.shadow.enabled && (
                    <VStack spacing={3} align="stretch" pl={4}>
                      <FormControl>
                        <FormLabel fontSize="sm">Shadow Color</FormLabel>
                        <Input
                          type="color"
                          value={settings.shadow.color}
                          onChange={(e) => handleNestedSettingsChange('shadow', 'color', e.target.value)}
                          size="sm"
                        />
                      </FormControl>
                      <FormControl>
                        <FormLabel fontSize="sm">Blur: {settings.shadow.blur}px</FormLabel>
                        <Slider
                          value={settings.shadow.blur}
                          onChange={(val) => handleNestedSettingsChange('shadow', 'blur', val)}
                          min={0}
                          max={50}
                        >
                          <SliderTrack>
                            <SliderFilledTrack />
                          </SliderTrack>
                          <SliderThumb />
                        </Slider>
                      </FormControl>
                    </VStack>
                  )}
                </VStack>
              </TabPanel>

              {/* Effects Tab */}
              <TabPanel>
                <VStack spacing={4} align="stretch">
                  <FormControl>
                    <FormLabel fontSize="sm">Brightness: {settings.filters.brightness}%</FormLabel>
                    <Slider
                      value={settings.filters.brightness}
                      onChange={(val) => handleNestedSettingsChange('filters', 'brightness', val)}
                      min={0}
                      max={200}
                    >
                      <SliderTrack>
                        <SliderFilledTrack />
                      </SliderTrack>
                      <SliderThumb />
                    </Slider>
                  </FormControl>

                  <FormControl>
                    <FormLabel fontSize="sm">Contrast: {settings.filters.contrast}%</FormLabel>
                    <Slider
                      value={settings.filters.contrast}
                      onChange={(val) => handleNestedSettingsChange('filters', 'contrast', val)}
                      min={0}
                      max={200}
                    >
                      <SliderTrack>
                        <SliderFilledTrack />
                      </SliderTrack>
                      <SliderThumb />
                    </Slider>
                  </FormControl>

                  <FormControl>
                    <FormLabel fontSize="sm">Saturation: {settings.filters.saturation}%</FormLabel>
                    <Slider
                      value={settings.filters.saturation}
                      onChange={(val) => handleNestedSettingsChange('filters', 'saturation', val)}
                      min={0}
                      max={200}
                    >
                      <SliderTrack>
                        <SliderFilledTrack />
                      </SliderTrack>
                      <SliderThumb />
                    </Slider>
                  </FormControl>

                  <FormControl>
                    <FormLabel fontSize="sm">Grayscale: {settings.filters.grayscale}%</FormLabel>
                    <Slider
                      value={settings.filters.grayscale}
                      onChange={(val) => handleNestedSettingsChange('filters', 'grayscale', val)}
                      min={0}
                      max={100}
                    >
                      <SliderTrack>
                        <SliderFilledTrack />
                      </SliderTrack>
                      <SliderThumb />
                    </Slider>
                  </FormControl>
                </VStack>
              </TabPanel>

              {/* Behavior Tab */}
              <TabPanel>
                <VStack spacing={4} align="stretch">
                  <FormControl>
                    <FormLabel fontSize="sm">Click Action</FormLabel>
                    <Select
                      value={settings.clickAction}
                      onChange={(e) => handleSettingsChange('clickAction', e.target.value)}
                      size="sm"
                    >
                      <option value="none">None</option>
                      <option value="lightbox">Open in Lightbox</option>
                      <option value="link">Open Link</option>
                    </Select>
                  </FormControl>

                  {settings.clickAction === 'link' && (
                    <FormControl>
                      <FormLabel fontSize="sm">Link URL</FormLabel>
                      <Input
                        value={settings.linkUrl || ''}
                        onChange={(e) => handleSettingsChange('linkUrl', e.target.value)}
                        placeholder="https://example.com"
                        size="sm"
                      />
                    </FormControl>
                  )}

                  {settings.clickAction === 'lightbox' && (
                    <FormControl>
                      <FormLabel fontSize="sm">Lightbox Caption</FormLabel>
                      <Input
                        value={settings.lightboxCaption || ''}
                        onChange={(e) => handleSettingsChange('lightboxCaption', e.target.value)}
                        placeholder="Image caption"
                        size="sm"
                      />
                    </FormControl>
                  )}

                  <FormControl display="flex" alignItems="center">
                    <FormLabel fontSize="sm" mb="0">Lazy Loading</FormLabel>
                    <Switch
                      isChecked={settings.lazy}
                      onChange={(e) => handleSettingsChange('lazy', e.target.checked)}
                    />
                  </FormControl>
                </VStack>
              </TabPanel>
            </TabPanels>
          </Tabs>
        </ModalBody>
      </ModalContent>
    </Modal>
  )

  if (!settings.src && isEditing) {
    return (
      <Box
        className={className}
        style={style}
        border="2px dashed"
        borderColor="gray.300"
        borderRadius="md"
        p={8}
        textAlign="center"
        bg={bgColor}
        cursor="pointer"
        onClick={onOpen}
      >
        <VStack spacing={3}>
          <SearchIcon boxSize={8} color="gray.400" />
          <Text color="gray.500" fontSize="sm">
            Click to add an image
          </Text>
          <Button size="sm" colorScheme="blue" onClick={onOpen}>
            Select Image
          </Button>
        </VStack>
        {renderImageEditor()}
      </Box>
    )
  }

  return (
    <Box className={className} position="relative" display="inline-block">
      {isEditing && (
        <IconButton
          aria-label="Edit image"
          icon={<EditIcon />}
          size="sm"
          position="absolute"
          top={2}
          right={2}
          zIndex={2}
          onClick={onOpen}
          colorScheme="blue"
        />
      )}

      <ChakraImage
        src={settings.src}
        alt={settings.alt}
        style={getImageStyle()}
        loading={settings.lazy ? 'lazy' : 'eager'}
        onClick={handleImageClick}
        onError={() => setError('Failed to load image')}
      />

      {settings.overlay.enabled && (
        <Box
          position="absolute"
          top="0"
          left="0"
          right="0"
          bottom="0"
          backgroundColor={settings.overlay.color}
          opacity={settings.overlay.opacity / 100}
          mixBlendMode={settings.overlay.blendMode as any}
          pointerEvents="none"
        />
      )}

      {error && (
        <Box
          position="absolute"
          top="50%"
          left="50%"
          transform="translate(-50%, -50%)"
          bg="red.100"
          color="red.600"
          p={2}
          borderRadius="md"
          fontSize="sm"
        >
          {error}
        </Box>
      )}

      {renderImageEditor()}

      {/* Lightbox Modal */}
      {showLightbox && (
        <Modal isOpen={showLightbox} onClose={() => setShowLightbox(false)} size="full">
          <ModalOverlay bg="blackAlpha.800" />
          <ModalContent bg="transparent" boxShadow="none">
            <ModalCloseButton color="white" size="lg" />
            <ModalBody display="flex" alignItems="center" justifyContent="center" p={0}>
              <VStack spacing={4}>
                <ChakraImage
                  src={settings.src}
                  alt={settings.alt}
                  maxH="90vh"
                  maxW="90vw"
                  objectFit="contain"
                />
                {settings.lightboxCaption && (
                  <Text color="white" textAlign="center" fontSize="lg">
                    {settings.lightboxCaption}
                  </Text>
                )}
              </VStack>
            </ModalBody>
          </ModalContent>
        </Modal>
      )}
    </Box>
  )
}

export default Image
