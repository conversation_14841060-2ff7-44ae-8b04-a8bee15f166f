'use client'

import React, { useState, useRef, useEffect } from 'react'
import {
  Box,
  VStack,
  HStack,
  Button,
  ButtonGroup,
  Select,
  Input,
  Popover,
  PopoverTrigger,
  PopoverContent,
  PopoverBody,
  IconButton,
  Divider,
  useColorModeValue,
  Tooltip
} from '@chakra-ui/react'
import {
  LinkIcon,
  HamburgerIcon,
  TriangleDownIcon,
  TriangleUpIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  EditIcon
} from '@chakra-ui/icons'

export interface HTMLTextSettings {
  content: string
  fontSize: string
  fontFamily: string
  fontWeight: string
  color: string
  textAlign: 'left' | 'center' | 'right' | 'justify'
  lineHeight: string
  letterSpacing: string
  textDecoration: string
  textTransform: 'none' | 'uppercase' | 'lowercase' | 'capitalize'
}

interface HTMLTextProps {
  settings: HTMLTextSettings
  onChange?: (settings: HTMLTextSettings) => void
  isEditing?: boolean
  placeholder?: string
  className?: string
  style?: React.CSSProperties
}

export function HTMLText({
  settings,
  onChange,
  isEditing = false,
  placeholder = 'Click to edit text...',
  className,
  style
}: HTMLTextProps) {
  const [isEditorOpen, setIsEditorOpen] = useState(false)
  const [localContent, setLocalContent] = useState(settings.content)
  const editorRef = useRef<HTMLDivElement>(null)
  const borderColor = useColorModeValue('gray.200', 'gray.700')

  useEffect(() => {
    setLocalContent(settings.content)
  }, [settings.content])

  const handleContentChange = () => {
    if (editorRef.current) {
      const newContent = editorRef.current.innerHTML
      setLocalContent(newContent)
      onChange?.({
        ...settings,
        content: newContent
      })
    }
  }

  const handleSettingsChange = (key: keyof HTMLTextSettings, value: any) => {
    onChange?.({
      ...settings,
      [key]: value
    })
  }

  const execCommand = (command: string, value?: string) => {
    document.execCommand(command, false, value)
    handleContentChange()
  }

  const insertLink = () => {
    const url = prompt('Enter URL:')
    if (url) {
      execCommand('createLink', url)
    }
  }

  const getTextStyle = (): React.CSSProperties => ({
    fontSize: settings.fontSize || '16px',
    fontFamily: settings.fontFamily || 'inherit',
    fontWeight: settings.fontWeight || 'normal',
    color: settings.color || '#000000',
    textAlign: settings.textAlign || 'left',
    lineHeight: settings.lineHeight || '1.5',
    letterSpacing: settings.letterSpacing || 'normal',
    textDecoration: settings.textDecoration || 'none',
    textTransform: settings.textTransform || 'none',
    ...style
  })

  if (isEditing && isEditorOpen) {
    return (
      <Box className={className}>
        {/* Rich Text Toolbar */}
        <Box
          p={2}
          border="1px"
          borderColor={borderColor}
          borderBottom="none"
          borderRadius="md"
          borderBottomRadius="none"
          bg={useColorModeValue('gray.50', 'gray.800')}
        >
          <VStack spacing={2}>
            {/* Formatting Controls */}
            <HStack spacing={1} wrap="wrap">
              <ButtonGroup size="sm" isAttached>
                <Tooltip label="Bold">
                  <Button
                    size="sm"
                    onClick={() => execCommand('bold')}
                    variant="outline"
                    fontWeight="bold"
                  >
                    B
                  </Button>
                </Tooltip>
                <Tooltip label="Italic">
                  <Button
                    size="sm"
                    onClick={() => execCommand('italic')}
                    variant="outline"
                    fontStyle="italic"
                  >
                    I
                  </Button>
                </Tooltip>
                <Tooltip label="Underline">
                  <Button
                    size="sm"
                    onClick={() => execCommand('underline')}
                    variant="outline"
                    textDecoration="underline"
                  >
                    U
                  </Button>
                </Tooltip>
              </ButtonGroup>

              <ButtonGroup size="sm" isAttached>
                <Tooltip label="Align Left">
                  <Button
                    size="sm"
                    onClick={() => execCommand('justifyLeft')}
                    variant="outline"
                  >
                    ⬅
                  </Button>
                </Tooltip>
                <Tooltip label="Align Center">
                  <Button
                    size="sm"
                    onClick={() => execCommand('justifyCenter')}
                    variant="outline"
                  >
                    ⬌
                  </Button>
                </Tooltip>
                <Tooltip label="Align Right">
                  <Button
                    size="sm"
                    onClick={() => execCommand('justifyRight')}
                    variant="outline"
                  >
                    ➡
                  </Button>
                </Tooltip>
              </ButtonGroup>

              <ButtonGroup size="sm" isAttached>
                <Tooltip label="Bullet List">
                  <Button
                    size="sm"
                    onClick={() => execCommand('insertUnorderedList')}
                    variant="outline"
                  >
                    •
                  </Button>
                </Tooltip>
                <Tooltip label="Numbered List">
                  <Button
                    size="sm"
                    onClick={() => execCommand('insertOrderedList')}
                    variant="outline"
                  >
                    1.
                  </Button>
                </Tooltip>
              </ButtonGroup>

              <Tooltip label="Insert Link">
                <IconButton
                  aria-label="Insert Link"
                  icon={<LinkIcon />}
                  onClick={insertLink}
                  variant="outline"
                  size="sm"
                />
              </Tooltip>

              <Popover>
                <PopoverTrigger>
                  <Button size="sm" variant="outline">
                    Color
                  </Button>
                </PopoverTrigger>
                <PopoverContent>
                  <PopoverBody>
                    <Input
                      type="color"
                      value={settings.color}
                      onChange={(e) => {
                        handleSettingsChange('color', e.target.value)
                        execCommand('foreColor', e.target.value)
                      }}
                      size="sm"
                    />
                  </PopoverBody>
                </PopoverContent>
              </Popover>
            </HStack>

            {/* Typography Controls */}
            <HStack spacing={2} wrap="wrap">
              <Select
                size="sm"
                value={settings.fontSize}
                onChange={(e) => handleSettingsChange('fontSize', e.target.value)}
                width="100px"
              >
                <option value="12px">12px</option>
                <option value="14px">14px</option>
                <option value="16px">16px</option>
                <option value="18px">18px</option>
                <option value="20px">20px</option>
                <option value="24px">24px</option>
                <option value="28px">28px</option>
                <option value="32px">32px</option>
                <option value="36px">36px</option>
                <option value="48px">48px</option>
              </Select>

              <Select
                size="sm"
                value={settings.fontFamily}
                onChange={(e) => handleSettingsChange('fontFamily', e.target.value)}
                width="150px"
              >
                <option value="inherit">Default</option>
                <option value="Arial, sans-serif">Arial</option>
                <option value="Helvetica, sans-serif">Helvetica</option>
                <option value="Georgia, serif">Georgia</option>
                <option value="Times New Roman, serif">Times New Roman</option>
                <option value="Courier New, monospace">Courier New</option>
                <option value="Verdana, sans-serif">Verdana</option>
              </Select>

              <Select
                size="sm"
                value={settings.fontWeight}
                onChange={(e) => handleSettingsChange('fontWeight', e.target.value)}
                width="100px"
              >
                <option value="normal">Normal</option>
                <option value="bold">Bold</option>
                <option value="lighter">Light</option>
                <option value="300">300</option>
                <option value="400">400</option>
                <option value="500">500</option>
                <option value="600">600</option>
                <option value="700">700</option>
              </Select>
            </HStack>

            <HStack>
              <Button
                size="sm"
                colorScheme="blue"
                onClick={() => setIsEditorOpen(false)}
              >
                Done
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => setIsEditorOpen(false)}
              >
                Cancel
              </Button>
            </HStack>
          </VStack>
        </Box>

        {/* Editable Content */}
        <Box
          ref={editorRef}
          contentEditable
          suppressContentEditableWarning
          onInput={handleContentChange}
          onBlur={handleContentChange}
          dangerouslySetInnerHTML={{ __html: localContent }}
          style={getTextStyle()}
          p={4}
          border="1px"
          borderColor={borderColor}
          borderTop="none"
          borderRadius="md"
          borderTopRadius="none"
          minH="100px"
          outline="none"
          _focus={{
            boxShadow: 'none'
          }}
        />
      </Box>
    )
  }

  return (
    <Box
      className={className}
      style={getTextStyle()}
      onClick={() => isEditing && setIsEditorOpen(true)}
      cursor={isEditing ? 'pointer' : 'default'}
      p={isEditing ? 2 : 0}
      border={isEditing ? '1px dashed' : 'none'}
      borderColor={isEditing ? 'gray.300' : 'transparent'}
      borderRadius="md"
      minH={isEditing ? '50px' : 'auto'}
      display="flex"
      alignItems={isEditing && !localContent ? 'center' : 'flex-start'}
      justifyContent={isEditing && !localContent ? 'center' : 'flex-start'}
    >
      {localContent ? (
        <Box dangerouslySetInnerHTML={{ __html: localContent }} />
      ) : (
        isEditing && (
          <Box color="gray.500" fontSize="sm">
            {placeholder}
          </Box>
        )
      )}
    </Box>
  )
}

export default HTMLText
