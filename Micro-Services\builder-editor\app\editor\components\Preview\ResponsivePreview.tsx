'use client'

import {
  Box,
  Flex,
  IconButton,
  Button,
  Text,
  useColorModeValue,
  ButtonGroup,
  Tooltip
} from '@chakra-ui/react'
import { CloseIcon, ExternalLinkIcon } from '@chakra-ui/icons'
import { useState } from 'react'
import { useEditorStore } from '@/lib/stores/editorStore'

interface ResponsivePreviewProps {
  onClose: () => void
}

export function ResponsivePreview({ onClose }: ResponsivePreviewProps) {
  const [previewBreakpoint, setPreviewBreakpoint] = useState<'desktop' | 'tablet' | 'mobile'>('desktop')
  
  const borderColor = useColorModeValue('gray.200', 'gray.700')
  const bgColor = useColorModeValue('white', 'gray.800')
  const previewBg = useColorModeValue('gray.100', 'gray.900')
  
  const { currentPage } = useEditorStore()

  const getPreviewDimensions = () => {
    switch (previewBreakpoint) {
      case 'desktop':
        return { width: '100%', height: '100%' }
      case 'tablet':
        return { width: '768px', height: '1024px' }
      case 'mobile':
        return { width: '375px', height: '667px' }
      default:
        return { width: '100%', height: '100%' }
    }
  }

  const dimensions = getPreviewDimensions()

  const handleOpenInNewTab = () => {
    // TODO: Implement opening preview in new tab
    console.log('Opening preview in new tab...')
  }

  if (!currentPage) {
    return (
      <Flex
        position="fixed"
        top="0"
        left="0"
        right="0"
        bottom="0"
        bg={bgColor}
        align="center"
        justify="center"
        zIndex="modal"
      >
        <Text>No page to preview</Text>
        <Button onClick={onClose} ml={4}>
          Close
        </Button>
      </Flex>
    )
  }

  return (
    <Box
      position="fixed"
      top="0"
      left="0"
      right="0"
      bottom="0"
      bg={bgColor}
      zIndex="modal"
    >
      {/* Preview Toolbar */}
      <Flex
        h="60px"
        bg={bgColor}
        borderBottom="1px"
        borderColor={borderColor}
        align="center"
        justify="space-between"
        px={4}
      >
        {/* Left Section */}
        <Flex align="center" gap={3}>
          <Tooltip label="Close Preview">
            <IconButton
              aria-label="Close preview"
              icon={<CloseIcon />}
              variant="ghost"
              size="sm"
              onClick={onClose}
            />
          </Tooltip>
          
          <Text fontSize="sm" fontWeight="medium" color="gray.700">
            Preview: {currentPage.name}
          </Text>
        </Flex>

        {/* Center Section - Device Controls */}
        <Flex align="center" gap={2}>
          <Text fontSize="xs" color="gray.500" mr={2}>
            Device:
          </Text>
          
          <ButtonGroup size="sm" isAttached variant="outline">
            <Button
              leftIcon={<span>🖥️</span>}
              colorScheme={previewBreakpoint === 'desktop' ? 'blue' : 'gray'}
              onClick={() => setPreviewBreakpoint('desktop')}
              fontSize="xs"
            >
              Desktop
            </Button>
            <Button
              leftIcon={<span>📱</span>}
              colorScheme={previewBreakpoint === 'tablet' ? 'blue' : 'gray'}
              onClick={() => setPreviewBreakpoint('tablet')}
              fontSize="xs"
            >
              Tablet
            </Button>
            <Button
              leftIcon={<span>📱</span>}
              colorScheme={previewBreakpoint === 'mobile' ? 'blue' : 'gray'}
              onClick={() => setPreviewBreakpoint('mobile')}
              fontSize="xs"
            >
              Mobile
            </Button>
          </ButtonGroup>
        </Flex>

        {/* Right Section */}
        <Flex align="center" gap={2}>
          <Tooltip label="Open in New Tab">
            <IconButton
              aria-label="Open in new tab"
              icon={<ExternalLinkIcon />}
              variant="ghost"
              size="sm"
              onClick={handleOpenInNewTab}
            />
          </Tooltip>
          
          <Button size="sm" colorScheme="blue" onClick={onClose}>
            Back to Editor
          </Button>
        </Flex>
      </Flex>

      {/* Preview Content */}
      <Flex
        h="calc(100vh - 60px)"
        bg={previewBg}
        align="center"
        justify="center"
        p={4}
      >
        <Box
          bg="white"
          borderRadius="lg"
          boxShadow="xl"
          overflow="hidden"
          style={{
            width: dimensions.width,
            height: dimensions.height,
            maxWidth: '100%',
            maxHeight: '100%'
          }}
        >
          {/* Preview Frame */}
          <Box
            h="100%"
            overflow="auto"
            position="relative"
          >
            {/* Render page sections */}
            {currentPage.sections.map((section) => (
              <Box
                key={section.id}
                style={{
                  ...section.style,
                  ...section.responsive[previewBreakpoint]
                }}
              >
                {/* Render section elements */}
                {section.elements.map((element) => (
                  <Box
                    key={element.id}
                    style={element.style}
                  >
                    {/* Simplified element rendering for preview */}
                    {element.type === 'text' && (
                      <Text {...element.props} style={element.style}>
                        {element.props.content || 'Text content'}
                      </Text>
                    )}
                    
                    {element.type === 'image' && (
                      <img
                        src={element.props.src || '/placeholder-image.jpg'}
                        alt={element.props.alt || 'Image'}
                        style={{
                          width: element.props.width || '100%',
                          height: element.props.height || 'auto',
                          ...element.style
                        }}
                      />
                    )}
                    
                    {element.type === 'button' && (
                      <button
                        style={{
                          padding: '8px 16px',
                          backgroundColor: '#3182ce',
                          color: 'white',
                          border: 'none',
                          borderRadius: '4px',
                          cursor: 'pointer',
                          ...element.style
                        }}
                      >
                        {element.props.text || 'Button'}
                      </button>
                    )}
                    
                    {/* Add more element types as needed */}
                  </Box>
                ))}
                
                {/* Empty section placeholder */}
                {section.elements.length === 0 && (
                  <Flex
                    minH="100px"
                    align="center"
                    justify="center"
                    color="gray.400"
                    fontSize="sm"
                  >
                    Empty section
                  </Flex>
                )}
              </Box>
            ))}
            
            {/* Empty page placeholder */}
            {currentPage.sections.length === 0 && (
              <Flex
                h="100%"
                align="center"
                justify="center"
                color="gray.400"
                fontSize="lg"
              >
                Empty page
              </Flex>
            )}
          </Box>
        </Box>
      </Flex>
    </Box>
  )
}
