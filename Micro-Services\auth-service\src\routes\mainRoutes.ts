import express from 'express'
import { asyncHandler } from '../middleware/asyncHandler'

const router = express.Router()

// Root endpoint
router.get('/', asyncHandler(async (req, res) => {
  res.json({
    success: true,
    data: {
      service: 'auth-service',
      version: '1.0.0',
      description: 'Authentication and authorization service for New Builder',
      endpoints: {
        auth: '/auth',
        health: '/health'
      }
    }
  })
}))

// Service info
router.get('/info', asyncHandler(async (req, res) => {
  res.json({
    success: true,
    data: {
      service: 'auth-service',
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      timestamp: new Date().toISOString()
    }
  })
}))

export default router

