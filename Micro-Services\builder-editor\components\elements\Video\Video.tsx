'use client'

import React, { useState, useRef } from 'react'
import {
  Box,
  VStack,
  HStack,
  Text,
  Input,
  Select,
  Button,
  FormControl,
  FormLabel,
  Switch,
  Slider,
  SliderTrack,
  SliderFilledTrack,
  SliderThumb,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  useColorModeValue,
  IconButton,
  Tooltip,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  AspectRatio
} from '@chakra-ui/react'
import { EditIcon, TriangleUpIcon, SettingsIcon } from '@chakra-ui/icons'

export interface VideoSettings {
  src: string
  type: 'upload' | 'youtube' | 'vimeo' | 'embed'
  poster?: string
  width: string
  height: string
  aspectRatio: '16:9' | '4:3' | '1:1' | '9:16' | 'custom'
  autoplay: boolean
  loop: boolean
  muted: boolean
  controls: boolean
  preload: 'none' | 'metadata' | 'auto'
  playsinline: boolean
  borderRadius: string
  shadow: {
    enabled: boolean
    x: number
    y: number
    blur: number
    spread: number
    color: string
  }
  overlay: {
    enabled: boolean
    color: string
    opacity: number
    showPlayButton: boolean
    playButtonSize: 'sm' | 'md' | 'lg'
    playButtonColor: string
  }
  responsive: boolean
  maxWidth: string
}

interface VideoProps {
  settings: VideoSettings
  onChange?: (settings: VideoSettings) => void
  isEditing?: boolean
  className?: string
  style?: React.CSSProperties
}

export function Video({
  settings,
  onChange,
  isEditing = false,
  className,
  style
}: VideoProps) {
  const [isPlaying, setIsPlaying] = useState(false)
  const [showOverlay, setShowOverlay] = useState(true)
  const { isOpen, onOpen, onClose } = useDisclosure()
  const videoRef = useRef<HTMLVideoElement>(null)
  
  const borderColor = useColorModeValue('gray.200', 'gray.700')
  const bgColor = useColorModeValue('gray.50', 'gray.800')

  const handleSettingsChange = (key: keyof VideoSettings, value: any) => {
    onChange?.({
      ...settings,
      [key]: value
    })
  }

  const handleNestedSettingsChange = (
    parent: keyof VideoSettings,
    key: string,
    value: any
  ) => {
    onChange?.({
      ...settings,
      [parent]: {
        ...(settings[parent] as any),
        [key]: value
      }
    })
  }

  const getVideoId = (url: string, type: string): string => {
    if (type === 'youtube') {
      const match = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/)
      return match ? match[1] : ''
    }
    if (type === 'vimeo') {
      const match = url.match(/vimeo\.com\/(\d+)/)
      return match ? match[1] : ''
    }
    return ''
  }

  const getEmbedUrl = (): string => {
    const videoId = getVideoId(settings.src, settings.type)
    
    switch (settings.type) {
      case 'youtube':
        return `https://www.youtube.com/embed/${videoId}?autoplay=${settings.autoplay ? 1 : 0}&loop=${settings.loop ? 1 : 0}&muted=${settings.muted ? 1 : 0}&controls=${settings.controls ? 1 : 0}`
      case 'vimeo':
        return `https://player.vimeo.com/video/${videoId}?autoplay=${settings.autoplay ? 1 : 0}&loop=${settings.loop ? 1 : 0}&muted=${settings.muted ? 1 : 0}`
      default:
        return settings.src
    }
  }

  const getContainerStyle = (): React.CSSProperties => {
    const boxShadow = settings.shadow.enabled
      ? `${settings.shadow.x}px ${settings.shadow.y}px ${settings.shadow.blur}px ${settings.shadow.spread}px ${settings.shadow.color}`
      : 'none'

    return {
      width: settings.responsive ? '100%' : settings.width || 'auto',
      maxWidth: settings.responsive ? settings.maxWidth || '100%' : 'none',
      borderRadius: settings.borderRadius || '0',
      boxShadow,
      overflow: 'hidden',
      position: 'relative',
      ...style
    }
  }

  const handlePlayClick = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause()
      } else {
        videoRef.current.play()
      }
      setIsPlaying(!isPlaying)
      setShowOverlay(false)
    }
  }

  const renderVideoContent = () => {
    if (settings.type === 'youtube' || settings.type === 'vimeo') {
      const aspectRatioMap = {
        '16:9': 16 / 9,
        '4:3': 4 / 3,
        '1:1': 1,
        '9:16': 9 / 16,
        'custom': undefined
      }

      return (
        <AspectRatio ratio={aspectRatioMap[settings.aspectRatio]}>
          <Box
            as="iframe"
            src={getEmbedUrl()}
            allowFullScreen
            width="100%"
            height="100%"
            border="none"
          />
        </AspectRatio>
      )
    }

    return (
      <Box position="relative">
        <Box
          as="video"
          ref={videoRef}
          src={settings.src}
          poster={settings.poster}
          autoPlay={settings.autoplay}
          loop={settings.loop}
          muted={settings.muted}
          controls={settings.controls}
          preload={settings.preload}
          playsInline={settings.playsinline}
          width="100%"
          height={settings.aspectRatio === 'custom' ? settings.height : 'auto'}
          style={{
            aspectRatio: settings.aspectRatio !== 'custom' ? settings.aspectRatio.replace(':', '/') : undefined
          }}
          onPlay={() => {
            setIsPlaying(true)
            setShowOverlay(false)
          }}
          onPause={() => setIsPlaying(false)}
        />

        {/* Custom Overlay */}
        {settings.overlay.enabled && showOverlay && (
          <Box
            position="absolute"
            top="0"
            left="0"
            right="0"
            bottom="0"
            backgroundColor={settings.overlay.color}
            opacity={settings.overlay.opacity / 100}
            display="flex"
            alignItems="center"
            justifyContent="center"
            cursor="pointer"
            onClick={handlePlayClick}
          >
            {settings.overlay.showPlayButton && (
              <IconButton
                aria-label="Play video"
                icon={<TriangleUpIcon transform="rotate(90deg)" />}
                size={settings.overlay.playButtonSize}
                colorScheme={settings.overlay.playButtonColor}
                borderRadius="full"
                fontSize={
                  settings.overlay.playButtonSize === 'lg' ? '2xl' :
                  settings.overlay.playButtonSize === 'md' ? 'xl' : 'lg'
                }
              />
            )}
          </Box>
        )}
      </Box>
    )
  }

  const renderVideoEditor = () => (
    <Modal isOpen={isOpen} onClose={onClose} size="xl">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>Video Settings</ModalHeader>
        <ModalCloseButton />
        <ModalBody pb={6}>
          <Tabs>
            <TabList>
              <Tab>Source</Tab>
              <Tab>Playback</Tab>
              <Tab>Appearance</Tab>
              <Tab>Overlay</Tab>
            </TabList>

            <TabPanels>
              {/* Source Tab */}
              <TabPanel>
                <VStack spacing={4} align="stretch">
                  <FormControl>
                    <FormLabel fontSize="sm">Video Type</FormLabel>
                    <Select
                      value={settings.type}
                      onChange={(e) => handleSettingsChange('type', e.target.value)}
                      size="sm"
                    >
                      <option value="upload">Upload/Direct URL</option>
                      <option value="youtube">YouTube</option>
                      <option value="vimeo">Vimeo</option>
                      <option value="embed">Custom Embed</option>
                    </Select>
                  </FormControl>

                  <FormControl>
                    <FormLabel fontSize="sm">
                      {settings.type === 'youtube' ? 'YouTube URL' :
                       settings.type === 'vimeo' ? 'Vimeo URL' :
                       'Video URL'}
                    </FormLabel>
                    <Input
                      value={settings.src}
                      onChange={(e) => handleSettingsChange('src', e.target.value)}
                      placeholder={
                        settings.type === 'youtube' ? 'https://www.youtube.com/watch?v=...' :
                        settings.type === 'vimeo' ? 'https://vimeo.com/...' :
                        'https://example.com/video.mp4'
                      }
                      size="sm"
                    />
                  </FormControl>

                  {settings.type === 'upload' && (
                    <FormControl>
                      <FormLabel fontSize="sm">Poster Image</FormLabel>
                      <Input
                        value={settings.poster || ''}
                        onChange={(e) => handleSettingsChange('poster', e.target.value)}
                        placeholder="https://example.com/poster.jpg"
                        size="sm"
                      />
                    </FormControl>
                  )}
                </VStack>
              </TabPanel>

              {/* Playback Tab */}
              <TabPanel>
                <VStack spacing={4} align="stretch">
                  <FormControl display="flex" alignItems="center">
                    <FormLabel fontSize="sm" mb="0">Autoplay</FormLabel>
                    <Switch
                      isChecked={settings.autoplay}
                      onChange={(e) => handleSettingsChange('autoplay', e.target.checked)}
                    />
                  </FormControl>

                  <FormControl display="flex" alignItems="center">
                    <FormLabel fontSize="sm" mb="0">Loop</FormLabel>
                    <Switch
                      isChecked={settings.loop}
                      onChange={(e) => handleSettingsChange('loop', e.target.checked)}
                    />
                  </FormControl>

                  <FormControl display="flex" alignItems="center">
                    <FormLabel fontSize="sm" mb="0">Muted</FormLabel>
                    <Switch
                      isChecked={settings.muted}
                      onChange={(e) => handleSettingsChange('muted', e.target.checked)}
                    />
                  </FormControl>

                  <FormControl display="flex" alignItems="center">
                    <FormLabel fontSize="sm" mb="0">Show Controls</FormLabel>
                    <Switch
                      isChecked={settings.controls}
                      onChange={(e) => handleSettingsChange('controls', e.target.checked)}
                    />
                  </FormControl>

                  <FormControl>
                    <FormLabel fontSize="sm">Preload</FormLabel>
                    <Select
                      value={settings.preload}
                      onChange={(e) => handleSettingsChange('preload', e.target.value)}
                      size="sm"
                    >
                      <option value="none">None</option>
                      <option value="metadata">Metadata</option>
                      <option value="auto">Auto</option>
                    </Select>
                  </FormControl>
                </VStack>
              </TabPanel>

              {/* Appearance Tab */}
              <TabPanel>
                <VStack spacing={4} align="stretch">
                  <FormControl>
                    <FormLabel fontSize="sm">Aspect Ratio</FormLabel>
                    <Select
                      value={settings.aspectRatio}
                      onChange={(e) => handleSettingsChange('aspectRatio', e.target.value)}
                      size="sm"
                    >
                      <option value="16:9">16:9 (Widescreen)</option>
                      <option value="4:3">4:3 (Standard)</option>
                      <option value="1:1">1:1 (Square)</option>
                      <option value="9:16">9:16 (Vertical)</option>
                      <option value="custom">Custom</option>
                    </Select>
                  </FormControl>

                  {settings.aspectRatio === 'custom' && (
                    <HStack>
                      <FormControl>
                        <FormLabel fontSize="sm">Width</FormLabel>
                        <Input
                          value={settings.width}
                          onChange={(e) => handleSettingsChange('width', e.target.value)}
                          placeholder="100%"
                          size="sm"
                        />
                      </FormControl>
                      <FormControl>
                        <FormLabel fontSize="sm">Height</FormLabel>
                        <Input
                          value={settings.height}
                          onChange={(e) => handleSettingsChange('height', e.target.value)}
                          placeholder="auto"
                          size="sm"
                        />
                      </FormControl>
                    </HStack>
                  )}

                  <FormControl display="flex" alignItems="center">
                    <FormLabel fontSize="sm" mb="0">Responsive</FormLabel>
                    <Switch
                      isChecked={settings.responsive}
                      onChange={(e) => handleSettingsChange('responsive', e.target.checked)}
                    />
                  </FormControl>

                  {settings.responsive && (
                    <FormControl>
                      <FormLabel fontSize="sm">Max Width</FormLabel>
                      <Input
                        value={settings.maxWidth}
                        onChange={(e) => handleSettingsChange('maxWidth', e.target.value)}
                        placeholder="100%"
                        size="sm"
                      />
                    </FormControl>
                  )}

                  <FormControl>
                    <FormLabel fontSize="sm">Border Radius</FormLabel>
                    <Input
                      value={settings.borderRadius}
                      onChange={(e) => handleSettingsChange('borderRadius', e.target.value)}
                      placeholder="0px"
                      size="sm"
                    />
                  </FormControl>

                  <FormControl display="flex" alignItems="center">
                    <FormLabel fontSize="sm" mb="0">Enable Shadow</FormLabel>
                    <Switch
                      isChecked={settings.shadow.enabled}
                      onChange={(e) => handleNestedSettingsChange('shadow', 'enabled', e.target.checked)}
                    />
                  </FormControl>
                </VStack>
              </TabPanel>

              {/* Overlay Tab */}
              <TabPanel>
                <VStack spacing={4} align="stretch">
                  <FormControl display="flex" alignItems="center">
                    <FormLabel fontSize="sm" mb="0">Enable Overlay</FormLabel>
                    <Switch
                      isChecked={settings.overlay.enabled}
                      onChange={(e) => handleNestedSettingsChange('overlay', 'enabled', e.target.checked)}
                    />
                  </FormControl>

                  {settings.overlay.enabled && (
                    <>
                      <FormControl>
                        <FormLabel fontSize="sm">Overlay Color</FormLabel>
                        <Input
                          type="color"
                          value={settings.overlay.color}
                          onChange={(e) => handleNestedSettingsChange('overlay', 'color', e.target.value)}
                          size="sm"
                        />
                      </FormControl>

                      <FormControl>
                        <FormLabel fontSize="sm">Opacity: {settings.overlay.opacity}%</FormLabel>
                        <Slider
                          value={settings.overlay.opacity}
                          onChange={(val) => handleNestedSettingsChange('overlay', 'opacity', val)}
                          min={0}
                          max={100}
                        >
                          <SliderTrack>
                            <SliderFilledTrack />
                          </SliderTrack>
                          <SliderThumb />
                        </Slider>
                      </FormControl>

                      <FormControl display="flex" alignItems="center">
                        <FormLabel fontSize="sm" mb="0">Show Play Button</FormLabel>
                        <Switch
                          isChecked={settings.overlay.showPlayButton}
                          onChange={(e) => handleNestedSettingsChange('overlay', 'showPlayButton', e.target.checked)}
                        />
                      </FormControl>

                      {settings.overlay.showPlayButton && (
                        <>
                          <FormControl>
                            <FormLabel fontSize="sm">Play Button Size</FormLabel>
                            <Select
                              value={settings.overlay.playButtonSize}
                              onChange={(e) => handleNestedSettingsChange('overlay', 'playButtonSize', e.target.value)}
                              size="sm"
                            >
                              <option value="sm">Small</option>
                              <option value="md">Medium</option>
                              <option value="lg">Large</option>
                            </Select>
                          </FormControl>

                          <FormControl>
                            <FormLabel fontSize="sm">Play Button Color</FormLabel>
                            <Select
                              value={settings.overlay.playButtonColor}
                              onChange={(e) => handleNestedSettingsChange('overlay', 'playButtonColor', e.target.value)}
                              size="sm"
                            >
                              <option value="blue">Blue</option>
                              <option value="red">Red</option>
                              <option value="green">Green</option>
                              <option value="purple">Purple</option>
                              <option value="orange">Orange</option>
                              <option value="gray">Gray</option>
                              <option value="white">White</option>
                              <option value="black">Black</option>
                            </Select>
                          </FormControl>
                        </>
                      )}
                    </>
                  )}
                </VStack>
              </TabPanel>
            </TabPanels>
          </Tabs>
        </ModalBody>
      </ModalContent>
    </Modal>
  )

  if (!settings.src && isEditing) {
    return (
      <Box
        className={className}
        style={style}
        border="2px dashed"
        borderColor="gray.300"
        borderRadius="md"
        p={8}
        textAlign="center"
        bg={bgColor}
        cursor="pointer"
        onClick={onOpen}
      >
        <VStack spacing={3}>
          <TriangleUpIcon boxSize={8} color="gray.400" transform="rotate(90deg)" />
          <Text color="gray.500" fontSize="sm">
            Click to add a video
          </Text>
          <Button size="sm" colorScheme="blue" onClick={onOpen}>
            Add Video
          </Button>
        </VStack>
        {renderVideoEditor()}
      </Box>
    )
  }

  return (
    <Box className={className} style={getContainerStyle()}>
      {isEditing && (
        <IconButton
          aria-label="Edit video"
          icon={<SettingsIcon />}
          size="sm"
          position="absolute"
          top={2}
          right={2}
          zIndex={3}
          onClick={onOpen}
          colorScheme="blue"
        />
      )}

      {renderVideoContent()}
      {renderVideoEditor()}
    </Box>
  )
}

export default Video
