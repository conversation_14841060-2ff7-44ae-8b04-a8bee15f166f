'use client'

import React, { useState } from 'react'
import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  VStack,
  HStack,
  Text,
  Button,
  Image,
  Box,
  Grid,
  Badge,
  Input,
  FormControl,
  FormLabel,
  useColorModeValue,
  Flex
} from '@chakra-ui/react'
import { motion } from 'framer-motion'
import { useEditorStore } from '@/lib/stores/editorStore'
import { useTranslation } from '@/lib/contexts/LanguageContext'

interface PageTemplate {
  id: string
  name: string
  description: string
  category: 'business' | 'portfolio' | 'blog' | 'ecommerce' | 'landing' | 'blank'
  thumbnail: string
  isPremium: boolean
  sections: any[]
}

interface PageTemplateModalProps {
  isOpen: boolean
  onClose: () => void
  onCreatePage: (template: PageTemplate, pageName: string) => void
}

const pageTemplates: PageTemplate[] = [
  {
    id: 'blank',
    name: 'Blank <PERSON>',
    description: 'Start with a clean slate',
    category: 'blank',
    thumbnail: '/api/placeholder-image?width=300&height=200&text=Blank%20Page&bg=f8f9fa&color=6c757d',
    isPremium: false,
    sections: []
  },
  {
    id: 'business-homepage',
    name: 'Business Homepage',
    description: 'Professional homepage with hero, services, and contact',
    category: 'business',
    thumbnail: '/api/placeholder-image?width=300&height=200&text=Business%20Homepage&bg=2563eb&color=ffffff',
    isPremium: false,
    sections: [
      {
        id: 'hero-section',
        type: 'hero',
        name: 'Hero Section',
        elements: [
          {
            id: 'hero-title',
            type: 'text',
            props: {
              content: 'Welcome to Our Business',
              fontSize: '48px',
              fontWeight: 'bold',
              textAlign: 'center',
              color: '#ffffff'
            },
            style: { marginBottom: '20px' }
          },
          {
            id: 'hero-subtitle',
            type: 'text',
            props: {
              content: 'We provide exceptional services to help your business grow',
              fontSize: '20px',
              textAlign: 'center',
              color: '#ffffff',
              opacity: 0.9
            },
            style: { marginBottom: '40px' }
          },
          {
            id: 'hero-cta',
            type: 'button',
            props: {
              text: 'Get Started',
              colorScheme: 'blue',
              size: 'lg'
            },
            style: {}
          }
        ],
        style: {
          padding: '100px 20px',
          backgroundColor: '#1a202c',
          color: 'white',
          textAlign: 'center'
        },
        responsive: { desktop: {}, tablet: {}, mobile: {} }
      },
      {
        id: 'services-section',
        type: 'feature',
        name: 'Our Services',
        elements: [
          {
            id: 'services-title',
            type: 'text',
            props: {
              content: 'Our Services',
              fontSize: '36px',
              fontWeight: 'bold',
              textAlign: 'center'
            },
            style: { marginBottom: '60px' }
          }
        ],
        style: {
          padding: '80px 20px',
          backgroundColor: '#ffffff'
        },
        responsive: { desktop: {}, tablet: {}, mobile: {} }
      }
    ]
  },
  {
    id: 'portfolio-showcase',
    name: 'Portfolio Showcase',
    description: 'Creative portfolio with gallery and about section',
    category: 'portfolio',
    thumbnail: '/api/placeholder-image?width=300&height=200&text=Portfolio%20Showcase&bg=7c3aed&color=ffffff',
    isPremium: true,
    sections: [
      {
        id: 'portfolio-hero',
        type: 'hero',
        name: 'Portfolio Hero',
        elements: [
          {
            id: 'portfolio-title',
            type: 'text',
            props: {
              content: 'Creative Portfolio',
              fontSize: '48px',
              fontWeight: 'bold',
              textAlign: 'center'
            },
            style: {}
          }
        ],
        style: {
          padding: '120px 20px',
          backgroundColor: '#2d3748',
          color: 'white',
          textAlign: 'center'
        },
        responsive: { desktop: {}, tablet: {}, mobile: {} }
      }
    ]
  },
  {
    id: 'blog-home',
    name: 'Blog Homepage',
    description: 'Blog layout with featured posts and sidebar',
    category: 'blog',
    thumbnail: '/api/placeholder-image?width=300&height=200&text=Blog%20Homepage&bg=059669&color=ffffff',
    isPremium: false,
    sections: []
  },
  {
    id: 'landing-page',
    name: 'Landing Page',
    description: 'High-converting landing page with testimonials',
    category: 'landing',
    thumbnail: '/api/placeholder-image?width=300&height=200&text=Landing%20Page&bg=dc2626&color=ffffff',
    isPremium: false,
    sections: []
  },
  {
    id: 'ecommerce-store',
    name: 'E-commerce Store',
    description: 'Product showcase with shopping features',
    category: 'ecommerce',
    thumbnail: '/api/placeholder-image?width=300&height=200&text=E-commerce%20Store&bg=ea580c&color=ffffff',
    isPremium: true,
    sections: []
  }
]

export function PageTemplateModal({ isOpen, onClose, onCreatePage }: PageTemplateModalProps) {
  const [selectedTemplate, setSelectedTemplate] = useState<PageTemplate | null>(null)
  const [pageName, setPageName] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const { t } = useTranslation()
  
  const borderColor = useColorModeValue('gray.200', 'gray.700')
  const hoverBg = useColorModeValue('gray.50', 'gray.700')

  const categories = [
    { id: 'all', name: 'All Templates' },
    { id: 'blank', name: 'Blank' },
    { id: 'business', name: 'Business' },
    { id: 'portfolio', name: 'Portfolio' },
    { id: 'blog', name: 'Blog' },
    { id: 'landing', name: 'Landing' },
    { id: 'ecommerce', name: 'E-commerce' }
  ]

  const filteredTemplates = selectedCategory === 'all' 
    ? pageTemplates 
    : pageTemplates.filter(template => template.category === selectedCategory)

  const handleCreatePage = () => {
    if (selectedTemplate && pageName.trim()) {
      onCreatePage(selectedTemplate, pageName.trim())
      setSelectedTemplate(null)
      setPageName('')
      onClose()
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'business': return 'blue'
      case 'portfolio': return 'purple'
      case 'blog': return 'green'
      case 'ecommerce': return 'orange'
      case 'landing': return 'red'
      case 'blank': return 'gray'
      default: return 'gray'
    }
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="6xl">
      <ModalOverlay />
      <ModalContent maxW="1200px" h="80vh">
        <ModalHeader>
          <Text fontSize="xl" fontWeight="bold">
            {t('page.new')} - Choose Template
          </Text>
        </ModalHeader>
        <ModalCloseButton />
        
        <ModalBody pb={6}>
          <VStack spacing={6} align="stretch" h="100%">
            {/* Category Filter */}
            <HStack spacing={2} wrap="wrap">
              {categories.map((category) => (
                <Button
                  key={category.id}
                  size="sm"
                  variant={selectedCategory === category.id ? 'solid' : 'outline'}
                  colorScheme={selectedCategory === category.id ? 'blue' : 'gray'}
                  onClick={() => setSelectedCategory(category.id)}
                >
                  {category.name}
                </Button>
              ))}
            </HStack>

            {/* Templates Grid */}
            <Box flex="1" overflowY="auto">
              <Grid templateColumns="repeat(auto-fill, minmax(280px, 1fr))" gap={4}>
                {filteredTemplates.map((template) => (
                  <motion.div
                    key={template.id}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Box
                      p={4}
                      border="2px"
                      borderColor={selectedTemplate?.id === template.id ? 'blue.500' : borderColor}
                      borderRadius="lg"
                      cursor="pointer"
                      _hover={{ bg: hoverBg }}
                      onClick={() => setSelectedTemplate(template)}
                    >
                      <VStack spacing={3} align="stretch">
                        {/* Template Thumbnail */}
                        <Box
                          h="150px"
                          bg="gray.100"
                          borderRadius="md"
                          overflow="hidden"
                          position="relative"
                        >
                          <Image
                            src={template.thumbnail}
                            alt={template.name}
                            fallback={
                              <Flex
                                h="100%"
                                align="center"
                                justify="center"
                                bg="gray.100"
                              >
                                <Text fontSize="sm" color="gray.500">
                                  {template.name}
                                </Text>
                              </Flex>
                            }
                            objectFit="cover"
                            w="100%"
                            h="100%"
                          />
                          
                          {template.isPremium && (
                            <Badge
                              position="absolute"
                              top="2"
                              right="2"
                              colorScheme="yellow"
                              variant="solid"
                              fontSize="xs"
                            >
                              PRO
                            </Badge>
                          )}
                        </Box>
                        
                        {/* Template Info */}
                        <Box>
                          <HStack justify="space-between" mb={1}>
                            <Text fontSize="sm" fontWeight="semibold" noOfLines={1}>
                              {template.name}
                            </Text>
                            <Badge
                              colorScheme={getCategoryColor(template.category)}
                              variant="subtle"
                              fontSize="xs"
                            >
                              {template.category}
                            </Badge>
                          </HStack>
                          
                          <Text fontSize="xs" color="gray.500" noOfLines={2}>
                            {template.description}
                          </Text>
                        </Box>
                      </VStack>
                    </Box>
                  </motion.div>
                ))}
              </Grid>
            </Box>

            {/* Page Creation Form */}
            {selectedTemplate && (
              <Box p={4} bg={useColorModeValue('gray.50', 'gray.800')} borderRadius="md">
                <VStack spacing={4}>
                  <FormControl>
                    <FormLabel fontSize="sm">Page Name</FormLabel>
                    <Input
                      value={pageName}
                      onChange={(e) => setPageName(e.target.value)}
                      placeholder="Enter page name..."
                      size="sm"
                    />
                  </FormControl>
                  
                  <HStack spacing={3} w="100%" justify="flex-end">
                    <Button size="sm" variant="outline" onClick={onClose}>
                      Cancel
                    </Button>
                    <Button
                      size="sm"
                      colorScheme="blue"
                      onClick={handleCreatePage}
                      isDisabled={!pageName.trim()}
                    >
                      Create Page
                    </Button>
                  </HStack>
                </VStack>
              </Box>
            )}
          </VStack>
        </ModalBody>
      </ModalContent>
    </Modal>
  )
}

export default PageTemplateModal
