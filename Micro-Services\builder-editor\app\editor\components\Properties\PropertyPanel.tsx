'use client'

import {
  Box,
  VStack,
  Text,
  Divider,
  useColorModeValue
} from '@chakra-ui/react'
import { useEditorStore } from '@/lib/stores/editorStore'
import { ElementProperties } from './ElementProperties'
import { SectionProperties } from './SectionProperties'
import { PageProperties } from './PageProperties'

export function PropertyPanel() {
  const borderColor = useColorModeValue('gray.200', 'gray.700')
  const bgColor = useColorModeValue('white', 'gray.800')
  
  const {
    selectedElement,
    selectedSection,
    currentPage
  } = useEditorStore()

  const renderContent = () => {
    if (selectedElement) {
      return <ElementProperties element={selectedElement} />
    }
    
    if (selectedSection) {
      return <SectionProperties section={selectedSection} />
    }
    
    if (currentPage) {
      return <PageProperties page={currentPage} />
    }
    
    return (
      <Box p={4} textAlign="center">
        <Text color="gray.500" fontSize="sm">
          Select an element, section, or page to edit properties
        </Text>
      </Box>
    )
  }

  return (
    <Box h="100%" bg={bgColor}>
      <Box
        p={3}
        borderBottom="1px"
        borderColor={borderColor}
      >
        <Text fontSize="sm" fontWeight="semibold" color="gray.600">
          Properties
        </Text>
      </Box>
      
      <Box h="calc(100% - 50px)" overflowY="auto">
        {renderContent()}
      </Box>
    </Box>
  )
}
